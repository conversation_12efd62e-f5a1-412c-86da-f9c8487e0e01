<?php

namespace app\controller;

use app\model\SchoolReport;
use app\model\Student;
use support\Request;
use think\facade\Db;
use Workerman\Coroutine;
use Workerman\Timer;
class IndexController
{
    protected $noNeedLogin = ['*']; // 不需要登录的方法
    public function index(Request $request)
    {
        static $readme;
        if (!$readme) {
            $readme = file_get_contents(base_path('README.md'));
        }
        return $readme;
    }

    public function view(Request $request)
    {
        return view('index/view', ['name' => 'webman']);
    }

    public function json(Request $request)
    {
        return json([
            "code"=>1
        ]);
    }

    public function getUserReport(Request $request) {
        $code = $request->get('idcode',"");
        $detail  = (new SchoolReport())->where('idcode',$code)->find();


        $stuData  = (new Student())->getDetail($detail['student_id']);
        $reportData  = (new SchoolReport())->getReportDetail($detail['id']);
        return json([
            "code" => 0,
            'msg' => 'success',
            'data' => ['studentInfo' => $stuData['data'], 'report' => $reportData['data']]
        ]);
    }
}
