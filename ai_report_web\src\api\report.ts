import request from '@/utils/request'
import {
  ApiResponse,
  Report,
  ReportListParams,
  ReportListResult,
  GenerateReportParams,
  getMsgStrParams,
  getSchoolAndMajorParams,
  SearchParams,
  AiRecommendationParams,
  AiRecommendationResult,
  ReportNumResult,
  SignalSchoolDetailInfo 
} from '@/types'




export function ai_recommendation(data: AiRecommendationParams): Promise<ApiResponse<AiRecommendationResult>> {
  return request({
    url: '/remote/ai_recommendation',
    method: 'post',
    data
  })
}

/**
 * SSE流式AI推荐院校接口
 * @param data - AI推荐参数
 * @param onMessage - 接收消息的回调函数
 * @returns Promise<void>
 */
export function streamAiRecommendation(
  data: AiRecommendationParams,
  onMessage: (data: any) => void
): Promise<void> {
  return new Promise((resolve, reject) => {
    const token = localStorage.getItem('token');
    const url = `http://127.0.0.1:8788/api/remote/stream_ai_recommendation?report_id=${data.report_id}&token=${token}`;
     //const url = `http://127.0.0.1:8888/api/ai_stream?report_id=${data.report_id}&token=${token}`;
    const eventSource = new EventSource(url);
    //let allData = "";
    // 常规消息处理
    const regexWithCapture = /([A-K]\.)/;
    let contentArr: string[] = []
    eventSource.onmessage = (event) => {
      try {
        const data = event.data.replace("\n", "")
       
        if( regexWithCapture.test(data)) {
            contentArr= data.split(regexWithCapture);
            contentArr.forEach(value => {
               if(value.trim() == '') return;
               console.log("value:",value)
               onMessage({ type: event.type, data:value});
            });
            return;
        }
        console.log("event", event)
        onMessage({ type: event.type, data});
      } catch (error) {
        console.error('解析SSE数据失败:', error);
      }
    };
    
    // 处理开始事件
    eventSource.addEventListener('start', (event: MessageEvent) => {
      console.log('SSE流开始:', event.data);
      onMessage({ type: 'start', data: event.data });
    });
    
    // 处理结束事件
    eventSource.addEventListener('end', (event: MessageEvent) => {
      onMessage({ type: 'end', data: event.data });
      eventSource.close();
      resolve();
    });
    
    // 处理错误事件
    eventSource.addEventListener('error', (event: MessageEvent) => {
      console.error('SSE错误事件:', event);
      try {
        const data = JSON.parse(event.data);
        onMessage({ type: 'error', error: data.error || '未知错误' });
        eventSource.close();
        reject(new Error(data.error || '未知错误'));
      } catch (e) {
        onMessage({ type: 'error', error: '解析错误数据失败' });
        eventSource.close();
        reject(new Error('解析错误数据失败'));
      }
    });
    
    // 连接错误处理
    eventSource.onerror = (error) => {
    
      console.error('SSE连接错误:', error);
      eventSource.close();
      reject(error);
    };
  });
}

/**
 * 获取报告列表
 * @param params - 查询参数
 * @returns Promise
 */
export function getReportList(params: ReportListParams): Promise<ApiResponse<ReportListResult>> {
  return request({
    url: '/report/list',
    method: 'get',
    params
  })
}

/**
 * 获取报告详情
 * @param id - 报告ID
 * @returns Promise
 */
export function getReportDetail(id: number | string): Promise<ApiResponse<AiRecommendationResult>> {
  return request({
    url: `/report/detail`,
    method: 'get',
    params: {
      report_id: id
    }
  })
}

/**
 * 根据报告ID获取学员基本信息
 * @param reportId - 报告ID
 * @returns Promise
 */
export function getReportBasicInfo(reportId: number | string): Promise<ApiResponse<any>> {
  return request({
    url: '/report/basic-info',
    method: 'get',
    params: {
      report_id: reportId
    }
  })
}

/**
 * 生成新报告
 * @param data - 报告数据
 * @returns Promise
 */
export function generateReport(data: GenerateReportParams): Promise<ApiResponse<Report>> {
  return request({
    url: '/report/generate',
    method: 'post',
    data
  })
}

/**
 * 删除报告
 * @param id - 报告ID
 * @returns Promise
 */
export function deleteReport(id: number | string): Promise<ApiResponse<null>> {
  return request({
    url: `/report/delete/${id}`,
    method: 'delete'
  })
}
/**
 * 获取推荐学校及分析prompt
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getReportBaseString(data: getMsgStrParams) : Promise<ApiResponse<Report>> {
  return request({
    url: '/remote/get_msg_str',
    method: 'post',
    data
  })
}

/**
 * 获取推荐学校及分析prompt
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getReportReasonString(data: getSchoolAndMajorParams) : Promise<ApiResponse<Report>>{
  return request({
    url: '/remote/school_and_major',
    method: 'post',
    data
  })
}


/**
 * 获取报告列表
 * @param {Object} data - 报告数据
 * @returns {Promise}
 */

export function getExamYears() : Promise<ApiResponse<Report>>{
  return request({
    url: '/get_years',
    method: 'get',
  })
}

/**
 * 获取报告列表
 * @param {SearchParams} params - 查询参数
 * @returns {Promise} 返回报告列表
 */
export function reportList(params: SearchParams) : Promise<ApiResponse<Report>>{
  return request({
    url: '/report',
    method: 'get',
    params
  })
}

/**
 * 检查爬虫登录状态
 * @returns Promise
 */
export function checkSpiderLogin(): Promise<ApiResponse<any>> {
  return request({
    url: '/check_spider_login',
    method: 'get'
  })
}

/**
 * 获取AI推荐院校
 * @param {Object} data - 包含report_id的对象
 * @returns {Promise}
 */
export function getAiRecommendation(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/ai_recommendation',
    method: 'post',
    data
  })
}

/**
 * 保存报告信息
 * @param data - 报告保存数据
 * @returns Promise
 */
export function saveReport(data: {
  report_id: number | string;
  recommendations: Array<{
    report_id: number | string;
    school_id: number;
    competition_difficulty: string;
    suggestions: string;
  }>;
  pdf_url?: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/save_report',
    method: 'post',
    data
  })
}

export function getReportNum(): Promise<ApiResponse<ReportNumResult>> {
  return request({
    url: '/report_count',
    method: 'get'
  })
}

/**
 * 获取流式AI推荐院校 - 已弃用，使用普通API代替
 * @deprecated 不再使用流式API，请使用getAiRecommendation
 */
// 移除流式API相关代码

/**
 * 获取单个学校的详细信息
 * @param data - 包含学校名称和报告ID的参数
 * @returns Promise
 */
export function getSchoolDetail(data: {
  school_name: string;
  report_id: number | string;
}): Promise<ApiResponse<SignalSchoolDetailInfo>> {
  return request({
    url: '/remote/get_school_detail_new',
    method: 'post',
    data
  })
}

export function getSchoolOverview(data: {
  report_id: number | string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_school_overview',
    method: 'post',
    data
  })
}

export function getReportDetailNew(data: {
  report_id: number | string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/api/report/detail',
    method: 'post',
    data
  })
}

export interface SaveReportDataParams {
  report_id: string | number;
  school_list: Array<{
    school_id: number;
    school_name: string;
    is_high_recommend: number; // 1 是高性价比推荐院校，0 不是
    difficulty_analysis: string;
    suggest: string;
    reason: string;
    score_formula?: string; // 总成绩计算公式
    study_years?: string; // 学制
    tuition_fee?: string; // 学费
    exam_subjects?: string; // 初试考试科目
    reference_books?: string; // 考试专业课参考书
    retest_content?: string; // 复试考核内容
    high_recommend_reason?: string; // 高性价比院校的推荐原因
    admission_requirements?: string; // 录取要求
  }>;
}

/**
 * 保存报告数据到ba_report_info表
 */
export const saveReportData = (params: SaveReportDataParams) => {
  return request({
    url: '/remote/save_report_data',
    method: 'post',
    data: params
  })
}

// 添加更新PDF URL的API函数
export function updatePdfUrl(data: {
  report_id: number | string;
  pdf_url: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/report/pdf-url',
    method: 'put',
    data
  })
}

// 添加获取COS密钥的API函数
export function getCosKey(): Promise<ApiResponse<any>> {
  return request({
    url: '/coskey',
    method: 'get'
  })
}

/**
 * 获取报告详情
 * @param id - 报告ID
 * @returns Promise
 */
export function reportByCode(idcode:  string): Promise<ApiResponse<any>> {
  return request({
    url: `/get_user_report`,
    method: 'get',
    params: {
      idcode: idcode
    }
  })
}

/**
 * 获取学习计划
 * @param data - 包含report_id的参数
 * @returns Promise
 */
export function getStudyPlan(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_study_plan',
    method: 'post',
    data
  })
}

/**
 * 从数据库获取学习计划
 * @param data - 包含report_id的参数
 * @returns Promise
 */
export function getStudyPlanFromDatabase(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_study_plan_from_database',
    method: 'post',
    data
  })
}


export function getStudyPlanFromDatabaseEdit(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_study_plan_from_database_edit',
    method: 'post',
    data
  })
}


/**
 * 更新薄弱模块分析
 * @param data - 薄弱模块数据
 * @returns Promise
 */
export function updateWeakModuleAnalysis(data: {
  id: number;
  subject: string;
  problem_analysis: string;
  solutions: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/update_weak_module_analysis',
    method: 'post',
    data
  })
}

/**
 * 更新学习模块
 * @param data - 学习模块数据
 * @returns Promise
 */
export function updateStudyModule(data: {
  id: number;
  name: string;
  study_content: string;
  study_method: string;
  study_materials: string;
  study_reminder: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/update_study_module',
    method: 'post',
    data
  })
}

/**
 * 更新综合建议
 * @param data - 综合建议数据
 * @returns Promise
 */
export function updateComprehensiveAdvice(data: {
  id: number;
  advice_content: string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/update_comprehensive_advice',
    method: 'post',
    data
  })
}

/**
 * 保存目标分数
 * @param data - 目标分数数据
 * @returns Promise
 */
export function saveTargetScores(data: {
  report_id: number | string;
  politics: number;
  english: number;
  business1: number;
  business2: number;
  total: number;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/save_target_scores',
    method: 'post',
    data
  })
}

/**
 * 获取目标分数
 * @param reportId - 报告ID
 * @returns Promise
 */
export function getTargetScores(reportId: number | string): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_target_scores',
    method: 'get',
    params: {
      report_id: reportId
    }
  })
}

export function generateCode(data: {
  report_id: number | string;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/generate_code',
    method: 'post',
    data
  })
}


/**
 * 获取报告推荐院校详情
 * @param reportId - 报告ID
 * @returns Promise
 */
export function getReportRecommendations(reportId: number | string): Promise<ApiResponse<any>> {
  return request({
    url: '/report/detail',
    method: 'get',
    params: {
      report_id: reportId
    }
  })
}

/**
 * 从数据库获取学习计划
 * @param data - 包含report_id的参数
 * @returns Promise
 */
export function getTargetScoreDetail(data: { report_id: number | string }): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/get_target_scores',
    method: 'get',
    data
  })
}

/**
 * 保存编辑字段到ba_report_info表
 * @param data - 编辑字段数据
 * @returns Promise
 */
export function saveReportFieldEdit(data: {
  report_id: number | string;
  school_id: number | string;
  field_name: string;
  field_value: string;
  is_high_recommend?: number;
}): Promise<ApiResponse<any>> {
  return request({
    url: '/remote/save_report_field_edit',
    method: 'post',
    data
  })
}

export default {
  getReportList,
  getReportDetail,
  generateReport,
  deleteReport,
  getReportBaseString,
  getReportReasonString,
  reportList,
  getExamYears,
  checkSpiderLogin,
  getAiRecommendation,
  getReportNum,
  getSchoolDetail,
  saveReportData,
  updatePdfUrl,
  getCosKey,
  getStudyPlan,
  getStudyPlanFromDatabase,
  updateWeakModuleAnalysis,
  updateStudyModule,
  updateComprehensiveAdvice,
  saveTargetScores,
  getTargetScores,
  getStudyPlanFromDatabaseEdit,
  saveReportFieldEdit,
}
