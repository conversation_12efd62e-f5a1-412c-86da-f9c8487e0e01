// 通用响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 用户相关类型
export interface UserInfo {
  id: number;
  username: string;
  nickname?: string;
  avatar?: string;
  roles?: string[];
  generated_report_times?: number;
  all_report_times?: number;
  permissions?: string[];
  mobile?: string;
  last_login_time?: number;
  last_login_ip?: string;
  [key: string]: any;
}

export interface LoginParams {
  username: string;
  password: string;
}

export interface LoginResult {
  token: string;
  user: UserInfo;
}

// 标签相关类型
export interface Tag {
  id: number;
  name: string;
  level: 1 | 2 | 3;
  parent_id: number;
  color?: string;
  sort?: number;
  create_time?: number;
  update_time?: number;
  is_delete?: number;
}

export interface AddTagParams {
  name: string;
  level: 1 | 2 | 3;
  parent_id: number;
  color?: string;
  sort?: number;
}

export interface EditTagParams {
  id: number;
  name: string;
  color?: string;
  sort?: number;
}

// 路由相关类型
export interface RouteMeta {
  title?: string;
  icon?: string;
  isAuth?: boolean;
  [key: string]: any;
}

// AI推荐相关类型
export interface AiRecommendationParams {
  report_id: number | string
}

// 年度统计信息
export interface YearlyStats {
  total_admission: number;
  avg_initial_score: string;
  max_initial_score: string;
  min_initial_score: string;
  avg_retest_score: string;
  max_retest_score: string;
  min_retest_score: string;
  year: number;
}

// 复试年度统计信息
export interface RetestYearlyStats {
  total_retest: number;
  avg_initial_score: string;
  max_initial_score: string;
  min_initial_score: string;
  admission_count: number;
  year: number;
  admission_rate: number;
}

// 录取统计信息
export interface AdmissionStats {
  yearly_stats?: YearlyStats[];
  has_data?: boolean;
}

// 复试统计信息
export interface RetestStats {
  yearly_stats?: RetestYearlyStats[];
  has_data?: boolean;
}

// 基本信息
export interface BasicInfo {
  research_direction?: string;
  exam_range?: string;
  reference_books?: string;
  retest_content?: string;
  tuition_fee?: string;
  study_years?: string;
  accommodation?: string;
  admission_requirements?: string;
  // 新增字段
  score_formula?: string; // C. 总成绩计算公式
  tuition_info?: string; // D. 学制与学费
  exam_subjects?: string; // E. 初试考试科目
  retest_score_requirement?: string; // G. 复试分数线基本要求
  admission_ratio?: string; // I. 录取比例与复试分数占比
}

// 学校信息
export interface SchoolInfo {
  id?: number;
  logo?: string;
  tag_211?: number;
  tag_985?: number;
  dual_class?: number;
  address?: string;
  phone?: string;
  home_site?: string;
  zsb_site?: string;
  is_211?: boolean;
  is_985?: boolean;
  is_dual_class?: boolean;
}

// 复试学生信息
export interface RetestStudent {
  name: string;
  college: string;
  major_code: string;
  major_name: string;
  politics_score: string;
  english_score: string;
  major1_score: string;
  major2_score: string;
  initial_score: string;
  volunteer_type: string;
  admission_status: string;
  year: number;
}

// 录取学生信息
export interface AdmissionStudent {
  name: string;
  college: string;
  major_code: string;
  major_name: string;
  initial_score: string;
  retest_score: string;
  total_score: string;
  first_choice_school: string;
  student_remark: string;
  year: number;
}

// 当年复试名单
export interface CurrentYearRetestList {
  list: RetestStudent[];
  year: number | null;
  count: number;
}

// 当年录取名单
export interface CurrentYearAdmissionList {
  list: AdmissionStudent[];
  year: number | null;
  count: number;
}

// 招生情况数据
export interface AdmissionData {
  year: number;
  planCount: number;
  examCount: number;
  admitCount: number;
  ratio: string;
  studentCount: number;
  highestScore: number;
  lowestScore: number;
  averageScore: number;
}

// 推荐学校信息
export interface RecommendSchool {
  school_name: string;
  major_name: string;
  difficulty_analysis: string;
  suggest: string;
  reason: string;
  school_id: string;
  admission_stats: AdmissionStats;
  retest_stats: RetestStats;
  basic_info: BasicInfo;
  school_info: SchoolInfo;
  current_year_retest_list: CurrentYearRetestList;
  current_year_admission_list: CurrentYearAdmissionList;
  admission_data?: AdmissionData[];
  is_high_recommend?: number; // 是否高性价比推荐院校：1-是，0-否
}

// 高推荐学校信息
export interface HighRecommendSchool {
  school_name?: string;
  major_name?: string;
  reason?: string;
  school_id?: string;
  admission_stats?: AdmissionStats;
  retest_stats?: RetestStats;
  basic_info?: BasicInfo;
  school_info?: SchoolInfo;
  current_year_retest_list?: CurrentYearRetestList;
  current_year_admission_list?: CurrentYearAdmissionList;
}

// 学校列表项
export interface SchoolListItem {
  id: number;
  school_name: string;
  region: string;
  city: string;
  college: string;
  major_name: string;
  major_code: string;
  min_score: number;
  score_diff: number;
  tags: string[];
  tag_985: boolean;
  tag_211: boolean;
  tag_double: boolean;
}

// AI推荐结果
export interface AiRecommendationResult {
  recommend_list: RecommendSchool[];
  high_recommend_list: HighRecommendSchool[];
  school_list: SchoolListItem[];
}

// AI推荐结果
export interface AiRecommendationEditResult {
  recommend_list: RecommendSchool[];
  high_recommend_list?: HighRecommendSchool | HighRecommendSchool[]; // 可选属性，兼容单个对象或数组
  school_list: SchoolListItem[];
}

export interface SignalSchoolDetailInfo{
  info:RecommendSchool,
  item:SchoolListItem,
  admission_data?: AdmissionData[]
}

// 导出其他模块的类型
export * from './report';
export * from './school';
