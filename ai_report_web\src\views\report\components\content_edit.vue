<template>
  <div class="content-container" v-show="isVisible">
    <!-- Loading状态 -->
    <div v-if="false" class="loading-container">
      <div class="loading-content">
        <!-- 倒计时显示 -->
        <div class="countdown-section">
          <div class="countdown-text">预计分析时间</div>
          <div class="countdown-timer">{{ formatTime(countdown) }}</div>
        </div>

        <!-- 动态效果文字 -->
        <div class="loading-text-section">
          <div class="loading-icon">
            <div class="spinner"></div>
          </div>
          <div class="dynamic-text">{{ currentLoadingText }}</div>
        </div>

        <!-- 进度条 -->
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: progressPercent + '%' }"
          ></div>
        </div>

        <!-- 加载步骤提示 -->
        <div class="loading-steps">
          <div
            class="step-item"
            :class="{ active: currentStep >= 1, completed: currentStep > 1 }"
          >
            <div class="step-circle">1</div>
            <div class="step-text">基本信息读取中</div>
          </div>
          <div
            class="step-item"
            :class="{ active: currentStep >= 2, completed: currentStep > 2 }"
          >
            <div class="step-circle">2</div>
            <div class="step-text">专业数据分析中</div>
          </div>
          <div
            class="step-item"
            :class="{ active: currentStep >= 3, completed: currentStep > 3 }"
          >
            <div class="step-circle">3</div>
            <div class="step-text">院校信息匹配中</div>
          </div>
          <div
            class="step-item"
            :class="{ active: currentStep >= 4, completed: currentStep > 4 }"
          >
            <div class="step-circle">4</div>
            <div class="step-text">生成分析报告中</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 原有内容 - 只在非loading状态显示 -->
    <div v-else>
      <div class="step-section">
        <div class="step-header">第三部分：院校总览</div>
        <div class="step-content">
          <div class="school-table-container">
            <!-- 表格头部 -->
            <div class="school-table-header">
              <div class="header-cell">序号</div>
              <div class="header-cell">院校名称</div>
              <div class="header-cell">所在地区</div>
              <div class="header-cell">所在省市</div>
              <div class="header-cell">学院</div>
              <div class="header-cell">专业</div>
              <div class="header-cell">专业代码</div>
              <div class="header-cell">总低分</div>
              <div class="header-cell">总低分分差</div>
            </div>

            <!-- 表格内容 -->
            <div class="school-table-body">
              <div
                class="table-row"
                v-for="(item, index) in reportData?.school_list || []"
                :key="index"
              >
                <div class="body-cell">{{ item.id }}</div>
                <div class="body-cell school-name">
                  <div>{{ item.school_name }}</div>
                  <div class="school-tags">
                    <span class="tag tag-985" v-if="item.tag_985">985</span>
                    <span class="tag tag-211" v-if="item.tag_211">211</span>
                    <span class="tag tag-double" v-if="item.tag_double"
                      >双一流</span
                    >
                  </div>
                </div>
                <div class="body-cell">{{ item.region }}</div>
                <div class="body-cell">{{ item.city }}</div>
                <div class="body-cell">{{ item.college }}</div>
                <div class="body-cell">{{ item.major_name }}</div>
                <div class="body-cell">{{ item.major_code }}</div>
                <div class="body-cell">{{ item.min_score }}</div>
                <div class="body-cell">{{ item.score_diff }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="step-section">
          <div class="step-header">第四部分：院校分析</div>

          <div
            class="step-content"
            v-for="(school, index) in reportData?.recommend_list || []"
            :key="index"
          >
            <div class="step-num-tag">
              <span>{{ index + 1 }}</span>
              <div class="tag-text">院校推荐</div>
            </div>

            <!-- 院校详情卡片 -->
            <div class="school-detail-card">
              <div class="school-header">
                <div class="school-logo">
                  <img
                    :src="
                      'https://' + school?.school_info?.logo ||
                      '@/assets/images/school.png'
                    "
                    alt="学校logo"
                  />
                </div>
                <div class="school-info">
                  <div class="school-title">
                    <h2>{{ school?.school_name || "" }}</h2>
                    <span class="school-location">{{
                      school?.school_info?.address || ""
                    }}</span>
                  </div>
                  <div class="school-tags-row">
                    <span
                      class="tag tag-double"
                      v-if="school?.school_info?.is_dual_class"
                      >双一流</span
                    >
                    <span
                      class="tag tag-985"
                      v-if="school?.school_info?.tag_985"
                      >985</span
                    >
                    <span
                      class="tag tag-211"
                      v-if="school?.school_info?.tag_211"
                      >211</span
                    >
                  </div>
                </div>
              </div>
              <h3 class="section-title">院校情况</h3>
              <div class="school-detail-section">
                <div class="detail-item">
                  <!-- <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div> -->
                  <!-- <div class="item-content">
                    <h4>总成绩计算公式</h4>
                    <p>
                      总成绩 = (初试成绩 + 5) × 初试权重 + 复试成绩 × 复试权重
                    </p>
                  </div> -->
                </div>

                <div class="detail-item">
                  <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div>
                  <div class="item-content">
                    <h4>学制</h4>
                    <p>{{ school?.basic_info?.study_years }}年</p>
                  </div>
                </div>

                <div class="detail-item">
                  <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div>
                  <div class="item-content">
                    <h4>学费</h4>
                    <p>学费：{{ school?.basic_info?.tuition_fee }}</p>
                  </div>
                </div>
              </div>
              <h3 class="section-title">初试模块</h3>
              <div class="school-detail-section">
                <div class="detail-item">
                  <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div>
                  <div class="item-content">
                    <h4>初试考试科目</h4>
                    {{ school?.basic_info?.exam_range }}
                  </div>
                </div>

                <div class="detail-item">
                  <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div>
                  <div class="item-content">
                    <h4>考试专业课参考书</h4>
                    <p>{{ school?.basic_info?.reference_books }}</p>
                  </div>
                </div>

                <!-- <div class="detail-item">
                  <div class="item-icon">
                    <img src="@/assets/images/tag.png" alt="标签图标" />
                  </div>
                  <div class="item-content">
                    <h4>复试考核内容</h4>
                    <p>
                      {{ school?.basic_info?.retest_content }}
                    </p>
                  </div>
                </div> -->
              </div>

              <!-- 第六步：招生情况 -->

              <div class="step-content-zs">
                <!-- 招生情况标题 -->
                <div class="admission-title">招生情况</div>

                <!-- 招生计划表格 -->
                <!-- <table class="admission-table">
                  <thead>
                    <tr>
                      <th>年份</th>
                      <th>招生计划</th>
                      <th>一志愿考试</th>
                      <th>录取数</th>
                      <th>录取比</th>
                      <th>调剂人数</th>
                      <th>最高分</th>
                      <th>最低分</th>
                      <th>平均分</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(item, index) in school.current_year_admission_list.list" :key="index">
                      <td>{{ item.year }}</td>
                      <td>{{ item.planCount }}</td>
                      <td>{{ item.examCount }}</td>
                      <td>{{ item.admitCount }}</td>
                      <td>{{ item.ratio }}</td>
                      <td>{{ item.studentCount }}</td>
                      <td>{{ item.highestScore }}</td>
                      <td>{{ item.lowestScore }}</td>
                      <td>{{ item.averageScore }}</td>
                    </tr>
                  </tbody>
                </table> -->

                <!-- 一志愿考试名单标题 -->
                <div class="admission-title">一志愿考试名单</div>

                <!-- 一志愿考试名单表格 -->
                <table class="admission-table">
                  <thead>
                    <tr>
                      <th>编号</th>
                      <th>学生姓名</th>
                      <th>政治</th>
                      <th>英语</th>
                      <th>专业课一</th>
                      <th>专业课二</th>
                      <th>初试成绩</th>
                      <th>是否一志愿</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, index) in school?.current_year_retest_list
                        ?.list || []"
                      :key="index"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>{{ item.name }}</td>
                      <td>{{ item.politics_score }}</td>
                      <td>{{ item.english_score }}</td>
                      <td>{{ item.major1_score }}</td>
                      <td>{{ item.major2_score }}</td>
                      <td>{{ item.initial_score }}</td>
                      <td>{{ item.admission_status }}</td>
                    </tr>
                  </tbody>
                </table>

                <!-- 复试模块标题 -->
                <div class="admission-title">复试模块</div>

                <!-- 复试模块内容 -->
                <div class="reexam-container">
                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img src="@/assets/images/tag.png" alt="标签图标" />
                      <div class="reexam-title">复试内容</div>
                    </div>
                    <div class="reexam-content">
                      {{ school?.basic_info?.retest_content }}
                    </div>
                  </div>

                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img src="@/assets/images/tag.png" alt="标签图标" />
                      <div class="reexam-title">复试录取比例</div>
                    </div>
                    <div class="reexam-content">
                      <p
                        v-if="
                          school?.current_year_retest_list?.count &&
                          school?.current_year_admission_list?.count
                        "
                      >
                        录取比例
                        {{
                          school.current_year_retest_list.count +
                          ":" +
                          school.current_year_admission_list.count
                        }}
                      </p>
                      <p v-else>暂无数据</p>
                    </div>
                  </div>
                </div>

                <!-- 报录取名单标题 -->
                <div class="admission-title">拟录取名单</div>

                <!-- 报录取名单表格 -->
                <table class="admission-table">
                  <thead>
                    <tr>
                      <th>编号</th>
                      <th>学生姓名</th>
                      <th>初试成绩</th>
                      <th>复试成绩</th>
                      <th>两项总成绩</th>
                      <th>一志愿学校</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(item, index) in school
                        ?.current_year_admission_list?.list || []"
                      :key="index"
                    >
                      <td>{{ index + 1 }}</td>
                      <td>{{ item.name }}</td>
                      <td>{{ item.initial_score }}</td>
                      <td>{{ item.retest_score }}</td>
                      <td>{{ item.total_score }}</td>
                      <td>{{ item.first_choice_school }}</td>
                    </tr>
                  </tbody>
                </table>

                <!-- 综合建议标题 -->
                <div class="admission-title">综合建议</div>

                <!-- 综合建议内容 -->
                <div class="reexam-container">
                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img src="@/assets/images/tag.png" alt="标签图标" />
                      <div class="reexam-title">竞争难度分析</div>
                      <el-button
                        v-if="!editingDifficulty[school.school_id]"
                        type="text"
                        size="small"
                        @click="startEditDifficulty(school.school_id)"
                        class="edit-btn"
                      >
                        编辑
                      </el-button>
                    </div>
                    <div class="reexam-content">
                      <div v-if="!editingDifficulty[school.school_id]">
                        {{ school.difficulty_analysis }}
                      </div>
                      <div v-else class="edit-container">
                        <el-input
                          v-model="editDifficultyText[school.school_id]"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入竞争难度分析"
                          class="edit-textarea"
                        />
                        <div class="edit-actions">
                          <el-button
                            size="small"
                            @click="saveDifficulty(school.school_id)"
                            >保存</el-button
                          >
                          <el-button
                            size="small"
                            @click="cancelEditDifficulty(school.school_id)"
                            >取消</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="reexam-card">
                    <div class="reexam-header">
                      <img src="@/assets/images/tag.png" alt="标签图标" />
                      <div class="reexam-title">备考目标建议</div>
                      <el-button
                        v-if="!editingSuggestion[school.school_id]"
                        type="text"
                        size="small"
                        @click="startEditSuggestion(school.school_id)"
                        class="edit-btn"
                      >
                        编辑
                      </el-button>
                    </div>
                    <div class="reexam-content">
                      <div v-if="!editingSuggestion[school.school_id]">
                        {{ school.suggest }}
                      </div>
                      <div v-else class="edit-container">
                        <el-input
                          v-model="editSuggestionText[school.school_id]"
                          type="textarea"
                          :rows="4"
                          placeholder="请输入备考目标建议"
                          class="edit-textarea"
                        />
                        <div class="edit-actions">
                          <el-button
                            size="small"
                            @click="saveSuggestion(school.school_id)"
                            >保存</el-button
                          >
                          <el-button
                            size="small"
                            @click="cancelEditSuggestion(school.school_id)"
                            >取消</el-button
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐综合性价比高的院校标题 -->
      <div class="step-num-tag">
        <span>03</span>
        <div class="tag-text">推荐综合性价比高的院校</div>
      </div>

      <!-- 推荐综合性价比高的院校内容 -->
      <div class="recommend-school-container">
        <div class="recommend-school-card">
          <div class="recommend-school-header">
            <div class="recommend-icon">
              <img src="@/assets/images/tag.png" alt="标签图标" />
            </div>
            <div class="recommend-title">推荐原因</div>
          </div>
          <div class="recommend-school-content">
            {{ 
              Array.isArray(reportData?.high_recommend_list) && reportData?.high_recommend_list.length > 0
                ? (reportData?.high_recommend_list[0]?.reason || "暂无推荐信息") 
                : "暂无推荐信息"
            }}
          </div>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-report-container" v-if="false">
        <el-button
          type="primary"
          size="large"
          @click="saveReport"
          :loading="saving"
          class="save-report-btn"
        >
          {{ saving ? "保存中..." : "保存报告" }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  onMounted,
  defineExpose,
  nextTick,
  type Ref,
  onUnmounted,
} from "vue";
import {  getReportDetail as ai_recommendation, saveReport as saveReportApi } from "@/api/report";
import { generateAndUploadPDF } from "@/utils/upload";
import { ElMessage } from "element-plus";

import type {AiRecommendationEditResult } from "@/types";
import router from "@/router";

// 本地特有的数据类型定义（index.ts 中没有的）

interface NationalLineData {
  subject_code: string;
  subject_name: string;
  years: string[];
  a_total: number[];
  a_single_100: number[];
  a_single_over100: number[];
  b_total: number[];
  b_single_100: number[];
  b_single_over100: number[];
}

interface ReportData {
  targetMajorName?: string;
  majorCode?: string;
  firstLevelSubject?: string;
}

// 扩展 window 对象类型
declare global {
  interface Window {
    reportData?: ReportData;
  }
}

// 国家线数据
const nationalLineData: Ref<NationalLineData> = ref({
  subject_code: "",
  subject_name: "",
  years: [],
  a_total: [],
  a_single_100: [],
  a_single_over100: [],
  b_total: [],
  b_single_100: [],
  b_single_over100: [],
});
onMounted(() => {

});




// 控制组件显示/隐藏的状态
const isVisible: Ref<boolean> = ref(false);

// 显示内容
const show = (): void => {
  console.log("显示content组件");
  isVisible.value = true;
  // 在组件显示后，需要重新初始化图表
  nextTick(() => {
    console.log("nextTick后初始化图表");

  });
};

// 隐藏内容
const hide = (): void => {
  isVisible.value = false;
};

// 切换显示/隐藏状态
const toggle = (): void => {
  isVisible.value = !isVisible.value;
  // 如果切换为显示状态，需要重新初始化图表
  if (isVisible.value) {
    nextTick(() => {

    });
  }
};
let reportData: Ref<AiRecommendationEditResult> = ref<AiRecommendationEditResult>(
  {} as AiRecommendationEditResult
);

// 编辑相关状态
const editingDifficulty = ref<Record<string, boolean>>({});
const editingSuggestion = ref<Record<string, boolean>>({});
const editDifficultyText = ref<Record<string, string>>({});
const editSuggestionText = ref<Record<string, string>>({});
const saving = ref(false);
const currentReportId = ref<string | number>("");

// Loading相关状态
const isLoading = ref(false);
const countdown = ref(180); // 180秒倒计时
const currentStep = ref(1);
const progressPercent = ref(0);
const currentLoadingText = ref("");

// 每个阶段对应的文字列表
const stageTexts = [
  ["基本信息读取中", "基本信息分析中"], // 阶段1
  ["专业数据获取中", "专业数据分析中"], // 阶段2
  ["院校信息匹配中", "院校数据分析中"], // 阶段3
  ["生成分析报告中", "报告内容优化中"], // 阶段4
];

// 完成状态的文字列表
const completionTexts = [
  "数据获取完成",
  "分析报告生成完成",
  "正在渲染报告内容",
  "即将为您展示结果",
];

let countdownTimer: NodeJS.Timeout | null = null;
let textChangeTimer: NodeJS.Timeout | null = null;
let stepTimer: NodeJS.Timeout | null = null;
let currentStageTextIndex = 0; // 当前阶段内的文字索引

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
};

// 开始loading效果
const startLoading = (): void => {
  isLoading.value = true;
  countdown.value = 180;
  currentStep.value = 1;
  progressPercent.value = 0;
  currentStageTextIndex = 0;

  // 设置第一个阶段的第一个文字
  currentLoadingText.value = stageTexts[0][0];

  // 倒计时
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
      // 更新进度条 (180秒 -> 100%)，但不超过100%且不倒退
      const newProgress = Math.round(((180 - countdown.value) / 180) * 100);
      if (newProgress > progressPercent.value) {
        progressPercent.value = newProgress;
      }
    } else {
      stopLoading();
    }
  }, 1000);

  // 动态文字切换 - 在当前阶段内切换文字
  textChangeTimer = setInterval(() => {
    const currentStageIndex = currentStep.value - 1; // 阶段索引从0开始
    const currentStageTextList = stageTexts[currentStageIndex];

    if (
      currentStageTextList &&
      currentStageTextIndex < currentStageTextList.length - 1
    ) {
      currentStageTextIndex++;
      currentLoadingText.value = currentStageTextList[currentStageTextIndex];
    }
    // 如果当前阶段的文字已经显示完，就保持最后一个文字不变
  }, 15000); // 每15秒切换一次文字（45秒阶段内显示2个文字，第一个显示15秒，第二个显示30秒）

  // 步骤进度更新
  stepTimer = setInterval(() => {
    if (currentStep.value < 4) {
      currentStep.value++;
      currentStageTextIndex = 0; // 重置阶段内文字索引

      // 设置新阶段的第一个文字
      const newStageIndex = currentStep.value - 1;
      if (stageTexts[newStageIndex]) {
        currentLoadingText.value = stageTexts[newStageIndex][0];
      }
    }
  }, 45000); // 每45秒更新一个步骤
};

// 停止loading效果
const stopLoading = (): void => {
  console.log("停止loading效果");
  isLoading.value = false;

  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
  if (textChangeTimer) {
    clearInterval(textChangeTimer);
    textChangeTimer = null;
  }
  if (stepTimer) {
    clearInterval(stepTimer);
    stepTimer = null;
  }

  // loading停止后，如果有国家线数据且组件可见，初始化图表
  nextTick(() => {
    console.log("loading停止后检查是否需要初始化图表");
    console.log("isVisible:", isVisible.value);
    console.log("nationalLineData:", nationalLineData.value);
    if (isVisible.value && nationalLineData.value.years.length > 0) {
      console.log("loading停止后初始化图表");

    }
  });
};

const emit = defineEmits(['updateOverlay']);

// 在需要控制父组件状态的地方调用
const toggleParentOverlay = () => {
  emit('updateOverlay', false); // 发送事件给父组件
};


// 显示完成状态动画
const showCompletionAnimation = (): void => {
  console.log("显示完成状态动画");

  // 停止倒计时定时器，防止进度条倒退
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }

  // 停止原有的文字切换定时器
  if (textChangeTimer) {
    clearInterval(textChangeTimer);
    textChangeTimer = null;
  }

  // 设置进度为100%
  progressPercent.value = 100;
  currentStep.value = 4;

  let completionIndex = 0;
  currentLoadingText.value = completionTexts[0];

  // 快速切换完成状态文字
  textChangeTimer = setInterval(() => {
    completionIndex = (completionIndex + 1) % completionTexts.length;
    currentLoadingText.value = completionTexts[completionIndex];
  }, 800); // 更快的切换速度，800ms一次
};

// 更新setReportId方法
const setReportId = async (report_id: string | number): Promise<void> => {
  currentReportId.value = report_id;
  // startLoading(); // 开始loading

  try {
    // const params: AiRecommendationParams = { report_id };
    const res = await ai_recommendation(report_id);
    
    // 强制类型转换，确保high_recommend_list是数组
    reportData.value = {
      ...res.data,
      high_recommend_list: Array.isArray(res.data.high_recommend_list) 
        ? res.data.high_recommend_list 
        : [res.data.high_recommend_list]
    };
    toggleParentOverlay()
    // 数据加载完成后，先显示完成状态动画
    // showCompletionAnimation();

    // 然后延迟几秒让完成动画播放完整再停止loading
    // 让用户看到完整的加载过程，提升用户体验
    // setTimeout(() => {
      //stopLoading();
    // }, 4000); // 延迟4秒，让完成动画播放完整（5个完成状态文字切换周期）
  } catch (error) {
    // console.error("加载报告数据失败:", error);
    ElMessage.error("加载报告数据失败，请稍后再试。");
    // 即使出错也要延迟停止loading，保持一致的用户体验
    setTimeout(() => {
        router.push("/report/list");
    }, 2000); // 出错时延迟2秒
  }
};

// 编辑相关方法
const startEditDifficulty = (schoolId: string) => {
  editingDifficulty.value[schoolId] = true;
  // 找到对应的学校数据
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editDifficultyText.value[schoolId] = school.difficulty_analysis || "";
  }
};

const startEditSuggestion = (schoolId: string) => {
  editingSuggestion.value[schoolId] = true;
  // 找到对应的学校数据
  const school = reportData.value.recommend_list?.find(
    (s) => s.school_id === schoolId
  );
  if (school) {
    editSuggestionText.value[schoolId] = school.suggest || "";
  }
};

const saveDifficulty = async (schoolId: string) => {
  try {
    // 更新本地数据
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      school.difficulty_analysis = editDifficultyText.value[schoolId];
    }

    // 立即保存到数据库
    await saveEditedContent();

    editingDifficulty.value[schoolId] = false;
    ElMessage.success("竞争难度分析保存成功");
  } catch (error) {
    console.error("保存竞争难度分析失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

const saveSuggestion = async (schoolId: string) => {
  try {
    // 更新本地数据
    const school = reportData.value.recommend_list?.find(
      (s) => s.school_id === schoolId
    );
    if (school) {
      school.suggest = editSuggestionText.value[schoolId];
    }

    // 立即保存到数据库
    await saveEditedContent();

    editingSuggestion.value[schoolId] = false;
    ElMessage.success("备考建议保存成功");
  } catch (error) {
    console.error("保存备考建议失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

// 保存编辑的内容到数据库
const saveEditedContent = async () => {
  if (!currentReportId.value) {
    throw new Error("报告ID不存在");
  }

  // 准备保存数据 - 使用 report_id 和 school_id 来标识记录
  const recommendations =
    reportData.value.recommend_list?.map((school) => ({
      report_id: currentReportId.value,
      school_id: parseInt(school.school_id),
      competition_difficulty: school.difficulty_analysis || "",
      suggestions: school.suggest || "",
    })) || [];

  // 调用后端API保存
  const saveData = {
    report_id: currentReportId.value,
    recommendations,
    pdf_url: "", // 编辑时不需要重新生成PDF
  };

  await saveReportApi(saveData);
};

const cancelEditDifficulty = (schoolId: string) => {
  editingDifficulty.value[schoolId] = false;
  delete editDifficultyText.value[schoolId];
};

const cancelEditSuggestion = (schoolId: string) => {
  editingSuggestion.value[schoolId] = false;
  delete editSuggestionText.value[schoolId];
};

// 保存报告方法
const saveReport = async () => {
  if (!currentReportId.value) {
    ElMessage.error("报告ID不存在");
    return;
  }

  saving.value = true;

  try {
    // 1. 获取当前页面的HTML内容，排除保存按钮
    const reportContainer = document.querySelector(".content-container");
    if (!reportContainer) {
      throw new Error("找不到报告内容容器");
    }

    // 克隆容器以避免修改原始DOM
    const clonedContainer = reportContainer.cloneNode(true) as HTMLElement;

    // 移除保存按钮
    const saveButtons = clonedContainer.querySelectorAll(
      ".save-report-container, .save-report-btn"
    );
    saveButtons.forEach((button) => button.remove());

    // 确保所有echarts图表可见
    const echartsElements = clonedContainer.querySelectorAll(
      "[_echarts_instance_]"
    );
    echartsElements.forEach((element: any) => {
      (element as HTMLElement).style.width = "100%";
      (element as HTMLElement).style.height = "400px";
      (element as HTMLElement).style.display = "block";
    });

    // 确保学员基本信息可见
    const infoElements = clonedContainer.querySelectorAll(
      ".student-info, .basic-info, .personal-info"
    );
    infoElements.forEach((element: any) => {
      (element as HTMLElement).style.display = "block";
      (element as HTMLElement).style.visibility = "visible";
      (element as HTMLElement).style.opacity = "1";
    });

    const htmlContent = clonedContainer.outerHTML;

    // 2. 生成PDF并上传到COS
    const uploadResult = await generateAndUploadPDF(
      htmlContent,
      `report_${currentReportId.value}.pdf`
    );
    const pdfUrl = uploadResult.Location;

    // 3. 准备保存数据 - 使用 report_id 和 school_id 来标识记录
    const recommendations =
      reportData.value.recommend_list?.map((school) => ({
        report_id: currentReportId.value,
        school_id: parseInt(school.school_id),
        competition_difficulty: school.difficulty_analysis || "",
        suggestions: school.suggest || "",
      })) || [];

    // 4. 调用后端API保存
    const saveData = {
      report_id: currentReportId.value,
      recommendations,
      pdf_url: pdfUrl,
    };

    await saveReportApi(saveData);

    ElMessage.success("报告保存成功");
  } catch (error) {
    console.error("保存报告失败:", error);
    ElMessage.error("保存报告失败: " + (error as Error).message);
  } finally {
    saving.value = false;
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  stopLoading();
});

// 测试加载文字切换逻辑（开发时使用）
const testLoadingTexts = (): void => {
  console.log("开始测试加载文字切换逻辑");
  console.log("阶段文字配置:", stageTexts);

  // 模拟阶段切换
  for (let stage = 1; stage <= 4; stage++) {
    console.log(`\n=== 阶段 ${stage} ===`);
    const stageIndex = stage - 1;
    const stageTextList = stageTexts[stageIndex];

    if (stageTextList) {
      stageTextList.forEach((text, index) => {
        console.log(`  文字 ${index + 1}: ${text}`);
      });
    }
  }
};

// 暴露方法给父组件
defineExpose({
  show,
  hide,
  toggle,
  isVisible,
  setReportId,
  startLoading,
  stopLoading,
  showCompletionAnimation,
  testLoadingTexts,
  isLoading,
  saveReport,
});
</script>

<style lang="less" scoped>
/* 步骤部分样式 */
.content-container {
  width: 100%;
  padding: 0 160px;
}

.step-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.step-header {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-image: url("@/assets/images/step-bg.png");
  background-repeat: no-repeat;
  padding-left: 92px;
  color: #fff;
  font-weight: bold;
}

.step-content {
  width: 100%;
  padding: 10px 20px;
}

.step-num-tag {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag {
  background-image: url("@/assets/images/subtitlebg.png");
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 图表样式 */
.echarts-box {
  width: 98%;
  height: 180px;
  margin: 0 auto 18px auto;
  border: 1px solid #1bb394;
  border-radius: 12px;
  background: #fff;
  box-sizing: border-box;
}

/* 学校表格样式 */
.school-table-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
}

.school-table-header {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  height: 46px;
  line-height: 46px;
  text-align: center;
  padding: 0 10px;
}

.header-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.school-table-body {
  background-color: #fff;
}

.table-row {
  display: grid;
  grid-template-columns: 0.5fr 2fr 0.8fr 1fr 1fr 1fr 1fr 0.8fr 0.8fr;
  border-bottom: 1px solid #f0f0f0;
  height: 60px;
  line-height: 60px;
  text-align: center;
  padding: 0 10px;

  &:hover {
    background-color: #f5f7fa;
  }

  &:nth-child(even) {
    background-color: #f9f9f9;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.body-cell {
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  color: #333;
}

.school-name {
  text-align: left;
  font-weight: bold;
  position: relative;
}

.school-tags {
  position: absolute;
  top: 40px;
  left: 10px;
  display: flex;
  gap: 4px;
}

.tag {
  display: inline-block;
  padding: 1px 6px;
  line-height: 1.5;
  font-size: 12px;
  border-radius: 3px;
  color: white;
  font-weight: normal;
}

.tag-985 {
  background-color: #ff9900;
}

.tag-211 {
  background-color: #8e6df8;
}

.tag-double {
  background-color: #1bb394;
}

/* 院校详情卡片样式 */
.school-detail-card {
  background-color: #fff;
  margin: 10px 0;
  overflow: hidden;
}

.school-header {
  display: flex;
  padding: 20px;
}

.school-logo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 90%;
    height: 90%;
    object-fit: contain;
  }
}

.school-info {
  flex: 1;
}

.school-title {
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;

  h2 {
    font-size: 22px;
    color: #333;
    margin: 0;
    margin-right: 12px;
  }

  .school-location {
    font-size: 14px;
    color: #666;
  }
}

.school-tags-row {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .tag {
    margin-right: 0;
  }

  .major-diff {
    font-size: 14px;
    color: #333;
    margin-left: 5px;
  }
}

.section-title {
  font-size: 18px;
  margin: 15px 0;
  font-weight: 600;
  position: relative;
  padding-left: 15px;
}

.school-detail-section {
  margin-bottom: 20px;
  padding: 10px 20px 20px;
  border-radius: 12px 12px 12px 12px;
  border: 1px solid #2fc293;
}

.detail-item {
  display: flex;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.item-content {
  flex: 1;

  h4 {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin: 0 0 5px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 招生情况样式 */
.step-content-zs {
  width: 100%;
  margin-top: 20px;
}

.admission-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 20px 0 15px 0;
  position: relative;
  padding-left: 12px;
}

.admission-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  border-radius: 8px;
  overflow: hidden;
}

.admission-table th {
  background-color: #dcf7f0;
  color: #333;
  font-weight: bold;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
}

.admission-table td {
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e8f5f0;
  background-color: #fff;
}

.admission-table tr:nth-child(even) td {
  background-color: #f9f9f9;
}

.reexam-container {
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.reexam-card {
  padding: 0 20px 20px;
}

.reexam-card:last-child {
  border-bottom: none;
}

.reexam-header {
  display: flex;
  align-items: center;
  padding: 15px 0;

  img {
    width: 27px;
    height: 21px;
    margin-right: 8px;
  }
}

.reexam-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.reexam-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.reexam-content p {
  margin: 8px 0;
}

.recommend-school-container-title {
  background-image: url(/src/assets/images/subtitlebg.png);
  width: 265px;
  height: 40px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #1bb394;
  font-weight: bold;
}

/* 推荐综合性价比高的院校样式 */
.recommend-school-container {
  margin-top: 28px;
  border: 1px solid #1bb394;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
  background-color: #fff;
}

.recommend-school-card {
  padding: 0 20px 20px;
}

.recommend-school-header {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.recommend-icon {
  margin-right: 8px;

  img {
    width: 27px;
    height: 21px;
  }
}

.recommend-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.recommend-school-content {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

.recommend-school-content p {
  margin: 8px 0;
}

/* Loading效果样式 - 改为悬浮效果 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  z-index: 9999;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.loading-content {
  text-align: center;
  background: white;
  padding: 50px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  position: relative;
  animation: slideUp 0.5s ease-out;
  border: 2px solid rgba(27, 179, 148, 0.2);
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 添加loading卡片的光晕效果 */
.loading-content::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #1bb394, #2fc293, #1bb394, #2fc293);
  border-radius: 22px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.8;
  }
}

.countdown-section {
  margin-bottom: 40px;
}

.countdown-text {
  font-size: 18px;
  color: #666;
  margin-bottom: 15px;
  font-weight: 500;
}

.countdown-timer {
  font-size: 48px;
  font-weight: bold;
  color: #1bb394;
  font-family: "Courier New", monospace;
  text-shadow: 0 2px 4px rgba(27, 179, 148, 0.3);
  animation: timerPulse 2s ease-in-out infinite;
}

@keyframes timerPulse {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.loading-text-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40px 0;
}

.loading-icon {
  margin-right: 20px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1bb394;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.dynamic-text {
  font-size: 20px;
  color: #333;
  font-weight: 600;
  animation: textFade 2s ease-in-out infinite;
}

@keyframes textFade {
  0%,
  100% {
    opacity: 1;
    transform: translateX(0);
  }

  50% {
    opacity: 0.7;
    transform: translateX(5px);
  }
}

.progress-bar {
  width: 100%;
  height: 12px;
  background-color: #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  margin: 30px 0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1bb394, #2fc293, #1bb394);
  border-radius: 6px;
  transition: width 0.8s ease;
  animation: progressGlow 2s ease-in-out infinite;
  background-size: 200% 100%;
  animation: progressGlow 2s ease-in-out infinite,
    progressMove 3s linear infinite;
}

@keyframes progressGlow {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

@keyframes progressMove {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

.loading-steps {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 40px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.step-item.active {
  background: linear-gradient(
    135deg,
    rgba(27, 179, 148, 0.1),
    rgba(47, 194, 147, 0.1)
  );
  border-color: #1bb394;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(27, 179, 148, 0.2);
}

.step-item.completed {
  background: linear-gradient(
    135deg,
    rgba(27, 179, 148, 0.15),
    rgba(47, 194, 147, 0.15)
  );
  border-color: #1bb394;
}

.step-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  background-color: #e0e0e0;
  color: #999;
  margin-right: 15px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.step-item.active .step-circle {
  background: linear-gradient(135deg, #1bb394, #2fc293);
  color: white;
  animation: stepPulse 1.5s ease-in-out infinite;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.4);
}

.step-item.completed .step-circle {
  background: linear-gradient(135deg, #1bb394, #2fc293);
  color: white;
  animation: none;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.3);
}

@keyframes stepPulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(27, 179, 148, 0.4);
  }

  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(27, 179, 148, 0.6);
  }
}

.step-text {
  font-size: 14px;
  color: #666;
  line-height: 1.3;
  transition: color 0.3s ease;
  font-weight: 500;
}

.step-item.active .step-text {
  color: #1bb394;
  font-weight: 600;
}

.step-item.completed .step-text {
  color: #1bb394;
  font-weight: 600;
}

/* 添加悬浮的粒子效果 */
.loading-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 20% 20%,
      rgba(27, 179, 148, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(47, 194, 147, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(27, 179, 148, 0.08) 0%,
      transparent 50%
    );
  animation: floatingParticles 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes floatingParticles {
  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.5;
  }

  50% {
    transform: translateY(-20px) scale(1.1);
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    padding: 40px 30px;
    margin: 20px;
  }

  .countdown-timer {
    font-size: 36px;
  }

  .dynamic-text {
    font-size: 18px;
  }

  .loading-steps {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .step-item {
    padding: 12px;
  }

  .step-circle {
    width: 40px;
    height: 40px;
    font-size: 16px;
    margin-right: 12px;
  }

  .step-text {
    font-size: 13px;
  }
}

/* 编辑功能样式 */
.edit-btn {
  margin-left: auto;
  color: #1bb394;
  font-size: 12px;
}

.edit-container {
  margin-top: 10px;
}

.edit-textarea {
  margin-bottom: 10px;
}

.edit-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 保存按钮样式 */
.save-report-container {
  display: flex;
  justify-content: center;
  padding: 30px 0;
  margin-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.save-report-btn {
  padding: 12px 40px;
  font-size: 16px;
  font-weight: bold;
  background: linear-gradient(135deg, #1bb394, #2fc293);
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(27, 179, 148, 0.3);
  transition: all 0.3s ease;
}

.save-report-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(27, 179, 148, 0.4);
}
</style>
