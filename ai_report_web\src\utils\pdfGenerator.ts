import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

interface PdfOptions {
  filename?: string
  quality?: number
  scale?: number
  useCORS?: boolean
  allowTaint?: boolean
  backgroundColor?: string
  pageHeight?: number
}

interface PageInfo {
  pageNumber: number
  totalPages: number
}

/**
 * 生成PDF的工具类 - 重新设计为完整内容渲染
 */
export class PdfGenerator {
  private static readonly A4_WIDTH = 210 // A4宽度(mm)
  private static readonly A4_HEIGHT = 297 // A4高度(mm)
  private static readonly MM_TO_PX = 3.78 // mm转px的比例
  private static readonly HEADER_HEIGHT = 15 // 页头高度(mm)
  private static readonly FOOTER_HEIGHT = 15 // 页脚高度(mm)

  /**
   * 将HTML元素转换为PDF - 完整内容渲染方案
   * @param element 要转换的HTML元素
   * @param options 配置选项
   */
  static async generatePdf(element: HTMLElement, options: PdfOptions = {}): Promise<void> {
    const {
      filename = `report-${Date.now()}.pdf`,
      quality = 0.95,
      scale = 2,
      useCORS = true,
      allowTaint = true,
      backgroundColor = '#ffffff',
      pageHeight = 1200 // 每页内容高度(px)
    } = options

    try {
      // 等待所有内容加载完成
      await this.waitForChartsAndImages(element)

      // 获取元素的完整尺寸
      const elementRect = element.getBoundingClientRect()
      const totalHeight = element.scrollHeight
      const elementWidth = element.scrollWidth

      console.log('元素尺寸:', { totalHeight, elementWidth, elementRect })

      // 创建PDF文档
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      })

      // 计算页面参数
      const pageWidth = this.A4_WIDTH
      const pdfPageHeight = this.A4_HEIGHT
      const contentWidth = pageWidth - 20 // 左右各留10mm边距
      const contentHeight = pdfPageHeight - this.HEADER_HEIGHT - this.FOOTER_HEIGHT // 为页头页脚留空间

      // 计算需要多少页 - 使用更合理的分页策略
      const maxContentHeightPerPage = contentHeight * this.MM_TO_PX / scale
      const pagesNeeded = Math.ceil(totalHeight / pageHeight)
      console.log('PDF生成信息:', {
        totalHeight,
        pageHeight,
        maxContentHeightPerPage,
        pagesNeeded,
        elementWidth
      })

      // 逐页生成PDF
      for (let pageIndex = 0; pageIndex < pagesNeeded; pageIndex++) {
        if (pageIndex > 0) {
          pdf.addPage()
        }

        console.log(`正在生成第 ${pageIndex + 1}/${pagesNeeded} 页...`)

        // 添加页头
        await this.addHeaderWithComponent(pdf, pageWidth)

        // 计算当前页的内容区域
        const startY = pageIndex * pageHeight
        const endY = Math.min(startY + pageHeight, totalHeight)
        const currentPageHeight = endY - startY

        console.log(`第${pageIndex + 1}页内容区域:`, {
          startY,
          endY,
          currentPageHeight
        })

        // 渲染当前页内容
        await this.renderPageContentByArea(pdf, element, {
          x: 10,
          y: this.HEADER_HEIGHT + 5,
          width: contentWidth,
          height: Math.min(currentPageHeight * this.MM_TO_PX / scale, contentHeight),
          scale,
          quality,
          useCORS,
          allowTaint,
          backgroundColor,
          scrollY: startY,
          elementWidth,
          pageHeight: currentPageHeight
        })

        // 添加页脚
        await this.addFooterWithComponent(pdf, pageWidth, pdfPageHeight, {
          pageNumber: pageIndex + 1,
          totalPages: pagesNeeded
        })

        // 添加进度提示
        console.log(`第 ${pageIndex + 1} 页生成完成`)
      }

      // 保存PDF
      pdf.save(filename)
      console.log('PDF生成完成')
    } catch (error) {
      console.error('PDF生成失败:', error)
      throw error
    }
  }

  /**
   * 按区域渲染页面内容
   */
  private static async renderPageContentByArea(
    pdf: jsPDF,
    element: HTMLElement,
    options: {
      x: number
      y: number
      width: number
      height: number
      scale: number
      quality: number
      useCORS: boolean
      allowTaint: boolean
      backgroundColor: string
      scrollY: number
      elementWidth: number
      pageHeight: number
    }
  ): Promise<void> {
    const { x, y, width, height, scale, quality, useCORS, allowTaint, backgroundColor, scrollY, elementWidth, pageHeight } = options

    try {
      // 使用html2canvas截取指定区域
      const canvas = await html2canvas(element, {
        scale,
        useCORS,
        allowTaint,
        backgroundColor,
        width: elementWidth,
        height: pageHeight,
        scrollX: 0,
        scrollY: scrollY,
        windowWidth: elementWidth,
        windowHeight: pageHeight,
        x: 0,
        y: scrollY
      })

      // 将canvas转换为图片并添加到PDF
      const imgData = canvas.toDataURL('image/jpeg', quality)
      const imgWidth = width
      const imgHeight = (canvas.height * width) / canvas.width

      pdf.addImage(imgData, 'JPEG', x, y, imgWidth, Math.min(imgHeight, height))
    } catch (error) {
      console.error('渲染页面内容失败:', error)
      throw error
    }
  }

  /**
   * 等待图表和图片加载完成
   */
  private static async waitForChartsAndImages(element: HTMLElement): Promise<void> {
    // 等待ECharts图表渲染完成
    const charts = element.querySelectorAll('.echarts-box')
    for (const chart of charts) {
      await new Promise(resolve => {
        const checkChart = () => {
          const canvas = chart.querySelector('canvas')
          if (canvas && canvas.width > 0 && canvas.height > 0) {
            resolve(void 0)
          } else {
            setTimeout(checkChart, 100)
          }
        }
        checkChart()
      })
    }

    // 等待图片加载完成
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(img => {
      return new Promise(resolve => {
        if (img.complete) {
          resolve(void 0)
        } else {
          img.onload = () => resolve(void 0)
          img.onerror = () => resolve(void 0)
        }
      })
    })

    await Promise.all(imagePromises)

    // 强制重绘ECharts图表以确保在PDF中正确显示
    const echartsInstances = (window as any).echarts?.getInstanceByDom
    if (echartsInstances) {
      charts.forEach(chart => {
        const instance = (window as any).echarts.getInstanceByDom(chart)
        if (instance) {
          instance.resize()
        }
      })
    }

    // 额外等待一段时间确保所有内容都已渲染
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  /**
   * 使用组件添加页头
   */
  private static async addHeaderWithComponent(pdf: jsPDF, pageWidth: number): Promise<void> {
    // 创建临时的页头HTML
    const headerHtml = `
      <div style="display: flex; align-items: center; justify-content: space-between; width: ${pageWidth * this.MM_TO_PX}px; height: ${this.HEADER_HEIGHT * this.MM_TO_PX}px; background: #1bb394; padding: 0 10px; box-sizing: border-box;">
        <div style="display: flex; align-items: center;">
          <div style="width: 82px; height: 34px; background: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf-top-green.png') no-repeat; background-size: contain;"></div>
          <div style="margin-left: 10px; width: 206px; height: 34px; background: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/top-text.png') no-repeat; background-size: contain;"></div>
        </div>
        <div style="height: 50px;">
          <img src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/pdf_logo.png" alt="Logo" style="height: 50px;" />
        </div>
      </div>
    `

    try {
      // 创建临时DOM元素
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = headerHtml
      tempDiv.style.position = 'absolute'
      tempDiv.style.top = '-9999px'
      tempDiv.style.left = '-9999px'
      document.body.appendChild(tempDiv)

      const headerElement = tempDiv.firstElementChild as HTMLElement

      // 等待图片加载
      await this.waitForImages(headerElement)

      // 使用html2canvas渲染页头
      const canvas = await html2canvas(headerElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#1bb394',
        width: pageWidth * this.MM_TO_PX,
        height: this.HEADER_HEIGHT * this.MM_TO_PX
      })

      // 添加到PDF
      const imgData = canvas.toDataURL('image/jpeg', 0.95)
      pdf.addImage(imgData, 'JPEG', 0, 0, pageWidth, this.HEADER_HEIGHT)

      // 清理临时元素
      document.body.removeChild(tempDiv)
    } catch (error) {
      console.error('页头渲染失败，使用简单页头:', error)
      // 降级到简单页头
      this.addSimpleHeader(pdf, pageWidth)
    }
  }

  /**
   * 使用组件添加页脚
   */
  private static async addFooterWithComponent(pdf: jsPDF, pageWidth: number, pageHeight: number, pageInfo: PageInfo): Promise<void> {
    const footerY = pageHeight - this.FOOTER_HEIGHT

    // 创建临时的页脚HTML
    const footerHtml = `
      <div style="position: relative; width: ${pageWidth * this.MM_TO_PX}px; height: ${this.FOOTER_HEIGHT * this.MM_TO_PX}px;">
        <div style="position: absolute; bottom: 20px; right: 40px; background: url('https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/bottom-left-bg.png') no-repeat; background-size: contain; width: 70px; height: 20px; font-weight: bold; font-size: 14px; padding-left: 18px; line-height: 20px;">
          ${pageInfo.pageNumber} / ${pageInfo.totalPages}
        </div>
      </div>
    `

    try {
      // 创建临时DOM元素
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = footerHtml
      tempDiv.style.position = 'absolute'
      tempDiv.style.top = '-9999px'
      tempDiv.style.left = '-9999px'
      document.body.appendChild(tempDiv)

      const footerElement = tempDiv.firstElementChild as HTMLElement

      // 等待图片加载
      await this.waitForImages(footerElement)

      // 使用html2canvas渲染页脚
      const canvas = await html2canvas(footerElement, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: 'transparent',
        width: pageWidth * this.MM_TO_PX,
        height: this.FOOTER_HEIGHT * this.MM_TO_PX
      })

      // 添加到PDF
      const imgData = canvas.toDataURL('image/png', 0.95)
      pdf.addImage(imgData, 'PNG', 0, footerY, pageWidth, this.FOOTER_HEIGHT)

      // 清理临时元素
      document.body.removeChild(tempDiv)
    } catch (error) {
      console.error('页脚渲染失败，使用简单页脚:', error)
      // 降级到简单页脚
      this.addSimpleFooter(pdf, pageWidth, pageHeight, pageInfo)
    }
  }

  /**
   * 简单页头（降级方案）
   */
  private static addSimpleHeader(pdf: jsPDF, pageWidth: number): void {
    // 添加绿色背景条
    pdf.setFillColor(27, 179, 148) // #1bb394
    pdf.rect(0, 0, pageWidth, this.HEADER_HEIGHT, 'F')

    // 添加标题文字
    pdf.setTextColor(255, 255, 255)
    pdf.setFontSize(10)
    pdf.text('AI择校报告系统', 10, 8)

    // 右侧添加日期
    const currentDate = new Date().toLocaleDateString('zh-CN')
    const dateWidth = pdf.getTextWidth(currentDate)
    pdf.text(currentDate, pageWidth - dateWidth - 10, 8)
  }

  /**
   * 简单页脚（降级方案）
   */
  private static addSimpleFooter(pdf: jsPDF, pageWidth: number, pageHeight: number, pageInfo: PageInfo): void {
    const footerY = pageHeight - 8

    // 添加分隔线
    pdf.setDrawColor(200, 200, 200)
    pdf.setLineWidth(0.5)
    pdf.line(10, footerY - 5, pageWidth - 10, footerY - 5)

    // 添加页码
    pdf.setTextColor(102, 102, 102)
    pdf.setFontSize(9)
    const pageText = `${pageInfo.pageNumber} / ${pageInfo.totalPages}`
    const textWidth = pdf.getTextWidth(pageText)
    pdf.text(pageText, (pageWidth - textWidth) / 2, footerY)

    // 左侧添加生成时间
    const generateTime = `生成时间: ${new Date().toLocaleString('zh-CN')}`
    pdf.setFontSize(8)
    pdf.text(generateTime, 10, footerY)
  }

  /**
   * 等待图片加载完成
   */
  private static async waitForImages(element: HTMLElement): Promise<void> {
    const images = element.querySelectorAll('img')
    const imagePromises = Array.from(images).map(img => {
      return new Promise(resolve => {
        if (img.complete) {
          resolve(void 0)
        } else {
          img.onload = () => resolve(void 0)
          img.onerror = () => resolve(void 0)
        }
      })
    })

    await Promise.all(imagePromises)
    await new Promise(resolve => setTimeout(resolve, 500))
  }
}

/**
 * 快捷方法：生成PDF
 * @param elementId 元素ID
 * @param options 配置选项
 */
export async function generatePdf(elementId: string, options: PdfOptions = {}): Promise<void> {
  const element = document.getElementById(elementId)
  if (!element) {
    throw new Error(`未找到ID为 ${elementId} 的元素`)
  }
  
  return PdfGenerator.generatePdf(element, options)
}

export default PdfGenerator
