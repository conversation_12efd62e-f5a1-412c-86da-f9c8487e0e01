<?php
namespace app\model;

use think\facade\Db;
use think\Model;

class SchoolReport extends Model
{
    protected $table = 'ba_school_report';
    protected $pk = 'id';
    protected $autoWriteTimestamp = false;

    /**
     * 地区倾向获取器
     * 将JSON字符串转换为数组
     * 数据格式: ["A", "B"]
     *
     * @param mixed $value 数据库中存储的JSON字符串
     * @return array 转换后的数组
     */
    public function getRegionPreferenceAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 地区倾向修改器
     * 将数组转换为JSON字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或JSON字符串
     * @return string 转换后的JSON字符串
     */
    public function setRegionPreferenceAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * 省份选择获取器
     * 将逗号分隔的字符串转换为数组
     * 数据格式: "北京市,上海市,广东省,..."
     *
     * @param mixed $value 数据库中存储的逗号分隔字符串
     * @return array 转换后的数组
     */
    public function getProvinceSelectionAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }

    /**
     * 省份选择修改器
     * 将数组转换为逗号分隔的字符串存储到数据库
     *
     * @param mixed $value 要存储的数组或字符串
     * @return string 转换后的逗号分隔字符串
     */
    public function setProvinceSelectionAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 学生关联
     * @return \think\model\relation\BelongsTo
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }


    // 获取报告的学校信息
    public function getReportDetail($reportId)
    {

        $tmpReportDetails = Db::name('school_report')->alias('sr')
            ->join('report_info ri', 'sr.id = ri.report_id')
            ->join('school_info si', 'ri.school_id = si.id')
            ->where('sr.id', $reportId)
            ->field('sr.id as report_id,si.province_city,ri.is_high_recommend, sr.undergraduate_major_name, si.school_name, si.province, si.area, si.school_type, ri.competition_difficulty,ri.suggestions,ri.reason_recommendation, ri.school_id')
            ->select();

        if ($tmpReportDetails->isEmpty()) {
            return ['code' => 1, 'msg' => '未找到报告详情', 'data' => []];
        }
        $recommendData  =  [];
        $highRecommendData = [];
        $reportDetails = [];
        if ($tmpReportDetails && method_exists($tmpReportDetails, 'toArray')) {
            $reportDetails = $tmpReportDetails->toArray();
        } elseif (is_array($tmpReportDetails)) {
            $reportDetails = $tmpReportDetails;
        }
        // var_dump($reportDetails);
        foreach ($reportDetails as $key => $value) {
            if(!$value['is_high_recommend']) {
                array_push($recommendData,
                    [
                        "school_name" => $value['school_name'],
                        "school_id" => $value['school_id'],
                        "major_name" => $value['undergraduate_major_name'],
                        "difficulty_analysis" =>$value['competition_difficulty'],
                        "suggest" => $value['suggestions'],
                        "reason" => $value['reason_recommendation'],
                        "province_city" => $value['province_city'],
                    ]);
            } else  {
                $highRecommendData =  [
                    "school_name" => $value['school_name'],
                    "school_id" => $value['school_id'],
                    "major_name" =>  $value['undergraduate_major_name'],
                    "reason" => $value['reason_recommendation'],
                ];
            }

        }
        foreach($recommendData as $index => &$school){
            //添加院校 id
            $school = $school + (new ReportInfo())->enrichSchoolData($school);

        }
        unset($school); // 解除引用
        $highRecommendData  = $highRecommendData + (new ReportInfo())->enrichSchoolData($highRecommendData);



        $recommendedSchools = [
            'recommend_list' =>$recommendData,
            'high_recommend_list' => $highRecommendData
        ];
        $schoolListData = (new ReportInfo())->extractSchoolListFromRecommendations($recommendedSchools, $reportId);


        return ['code' => 0, 'msg' => 'success',  'data' => array_merge($recommendedSchools, ['school_list' => $schoolListData])];
    }
}
