<template>
  <div
    class="weak-module-analysis"
    v-show="isVisible"
    v-loading="dataLoading"
    element-loading-text="正在加载学习计划数据..."
  >
    <img
      width="320"
      src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/study_sche.png"
    />
    <!-- 标题部分 -->
    <div class="step-num-tag">
      <span>01</span>
      <div class="tag-text">薄弱模块分析</div>
    </div>

    <!-- 薄弱模块容器 -->

    <div class="weak-module-container" v-if="weakModuleData.length > 0">
      <div
        v-for="(weakModule, moduleIndex) in weakModuleData"
        :key="moduleIndex"
      >
        <!-- 科目 -->
        <div class="detail-section">
          <div class="subtitle">
            <img
              width="26"
              src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
              alt="标签图标"
            />
            <span class="subtitle-text">科目</span>
          </div>
          <div class="subtitle-container">
            <!-- 编辑模式 -->
            <el-input
              v-if="editStates.weakModules[moduleIndex]"
              v-model="editData.weakModules[moduleIndex].subject"
              placeholder="请输入科目名称"
              class="edit-input analysis-text"
            />
            <!-- 显示模式 -->
            <p v-else class="detail-text">
              <span
                v-text="
                  typewriterStates.weakModules[moduleIndex]?.subject?.text || ''
                "
              ></span>
              <span
                v-if="
                  typewriterStates.weakModules[moduleIndex]?.subject?.isTyping
                "
                class="typing-cursor"
                >|</span
              >
            </p>
            <!-- 编辑按钮 -->
            <div class="edit-button-container">
              <el-button
                v-if="!editStates.weakModules[moduleIndex]"
                type="text"
                size="small"
                @click="startEditWeakModule(moduleIndex)"
                :icon="Edit"
                class="edit-btn"
              >
                编辑
              </el-button>
              <div v-else class="edit-actions">
                <el-button
                  size="small"
                  @click="saveWeakModule(moduleIndex)"
                  :loading="loading"
                  class="save-btn"
                >
                  保存
                </el-button>
                <el-button
                  size="small"
                  @click="cancelEditWeakModule(moduleIndex)"
                >
                  取消
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 问题分析 -->
        <div
          class="analysis-section"
          v-if="
            typewriterStates.weakModules[moduleIndex]?.problemAnalysis?.text ||
            editStates.weakModules[moduleIndex]
          "
        >
          <div class="subtitle">
            <img
              width="26"
              src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
              alt="标签图标"
            />
            <span class="subtitle-text">问题分析</span>
          </div>
          <!-- 编辑模式 -->
          <el-input
            v-if="editStates.weakModules[moduleIndex]"
            v-model="editData.weakModules[moduleIndex].problemAnalysis"
            type="textarea"
            :rows="4"
            placeholder="请输入问题分析"
            class="edit-textarea analysis-text"
          />
          <!-- 显示模式 -->
          <p v-else class="analysis-text">
            <span
              v-text="
                typewriterStates.weakModules[moduleIndex]?.problemAnalysis
                  ?.text || ''
              "
            ></span>
            <span
              v-if="
                typewriterStates.weakModules[moduleIndex]?.problemAnalysis
                  ?.isTyping
              "
              class="typing-cursor"
              >|</span
            >
          </p>
        </div>

        <!-- 解决方案 -->
        <div
          class="solutions-section"
          v-if="
            typewriterStates.weakModules[moduleIndex]?.solutions?.text ||
            editStates.weakModules[moduleIndex]
          "
        >
          <div class="subtitle">
            <img
              width="26"
              src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
              alt="标签图标"
            />
            <span class="subtitle-text">解决方案</span>
          </div>
          <div class="solutions-content">
            <!-- 编辑模式 -->
            <el-input
              v-if="editStates.weakModules[moduleIndex]"
              v-model="editData.weakModules[moduleIndex].solutions"
              type="textarea"
              :rows="6"
              placeholder="请输入解决方案"
              class="edit-textarea"
            />
            <!-- 显示模式 -->
            <p v-else class="solution-text">
              <span
                v-text="
                  typewriterStates.weakModules[moduleIndex]?.solutions?.text ||
                  ''
                "
              ></span>
              <span
                v-if="
                  typewriterStates.weakModules[moduleIndex]?.solutions?.isTyping
                "
                class="typing-cursor"
                >|</span
              >
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 目标分数表格 -->
    <div class="target-score-section">
      <div class="target-score-header">
        <div class="target-score-title-container">
          <img
            width="26"
            src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
            alt="标签图标"
          />
          <span class="target-score-title">目标分数</span>
        </div>
        <div class="target-score-actions">
          <el-button
            size="small"
            @click="saveTargetScores"
            :loading="savingTargetScores"
            class="save-btn"
          >
            保存
          </el-button>
        </div>
      </div>

      <div class="target-score-table">
        <div class="score-row header-row">
          <div class="score-cell">政治</div>
          <div class="score-cell">英语</div>
          <div class="score-cell">业务课一</div>
          <div class="score-cell">业务课二</div>
          <div class="score-cell">总分</div>
        </div>
        <div class="score-row data-row">
          <div class="score-cell bottom-cell">
            <input class="score-input" v-model.number="formData.politics" />
          </div>
          <div class="score-cell bottom-cell">
            <input class="score-input" v-model.number="formData.english" />
          </div>
          <div class="score-cell bottom-cell">
            <input class="score-input" v-model.number="formData.business1" />
          </div>
          <div class="score-cell bottom-cell">
            <input class="score-input" v-model.number="formData.business2" />
          </div>
          <div class="score-cell bottom-cell">
            <input class="score-input" v-model.number="total" disabled />
          </div>
        </div>
      </div>
    </div>

    <!-- 学习规划模块 -->
    <div
      class="study-planning-section"
      v-if="studyPlanningData.stages.length > 0"
    >
      <!-- 主标题 -->
      <div class="step-num-tag">
        <span>03</span>
        <div class="tag-text">{{ studyPlanningData.title }}</div>
      </div>

      <!-- 阶段循环输出 -->
      <div
        v-for="(stage, stageIndex) in studyPlanningData.stages"
        :key="stage.id"
        class="stage-section"
      >
        <!-- 阶段标题 -->
        <div
          class="stage-title"
          v-if="typewriterStates.studyPlanning.stages[stageIndex]?.title.text"
        >
          <span
            v-text="
              typewriterStates.studyPlanning.stages[stageIndex].title.text
            "
          ></span>
          <span
            v-if="
              typewriterStates.studyPlanning.stages[stageIndex].title.isTyping
            "
            class="typing-cursor"
            >|</span
          >
        </div>

        <div
          class="stage-content"
          v-if="typewriterStates.studyPlanning.stages[stageIndex]?.title.text"
        >
          <!-- 模块容器循环输出 -->
          <div
            v-for="(module, moduleIndex) in stage.modules"
            :key="module.id"
            class="module-container"
          >
            <!-- 模块标题 -->

            <div
              class="module-title"
              v-if="
                typewriterStates.studyPlanning.stages[stageIndex]?.modules[
                  moduleIndex
                ]?.name.text ||
                editStates.studyModules[`${stageIndex}-${moduleIndex}`]
              "
            >
              <!-- 编辑模式 -->
              <el-input
                v-if="editStates.studyModules[`${stageIndex}-${moduleIndex}`]"
                v-model="
                  editData.studyModules[`${stageIndex}-${moduleIndex}`].name
                "
                placeholder="请输入模块名称"
                class="edit-input"
              />
              <!-- 显示模式 -->
              <div v-else>
                <span
                  class="module-title-text"
                  v-text="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].name.text
                  "
                ></span>
                <span
                  v-if="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].name.isTyping
                  "
                  class="typing-cursor"
                  >|</span
                >
              </div>

              <!-- 编辑按钮 -->
              <div class="edit-button-container">
                <el-button
                  v-if="
                    !editStates.studyModules[`${stageIndex}-${moduleIndex}`]
                  "
                  type="text"
                  size="small"
                  @click="startEditStudyModule(stageIndex, moduleIndex)"
                  :icon="Edit"
                  class="edit-btn"
                >
                  编辑
                </el-button>
                <div v-else class="edit-actions">
                  <el-button
                    size="small"
                    @click="saveStudyModule(stageIndex, moduleIndex)"
                    :loading="loading"
                    class="save-btn"
                  >
                    保存
                  </el-button>
                  <el-button
                    size="small"
                    @click="cancelEditStudyModule(stageIndex, moduleIndex)"
                  >
                    取消
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 学习内容 -->
            <div
              class="study-item"
              v-if="
                typewriterStates.studyPlanning.stages[stageIndex]?.modules[
                  moduleIndex
                ]?.studyContent.text ||
                editStates.studyModules[`${stageIndex}-${moduleIndex}`]
              "
            >
              <div class="study-item-title">
                <img
                  width="26"
                  src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                  alt="菱形图标"
                />
                <span class="study-item-title-text">学习内容</span>
              </div>
              <!-- 编辑模式 -->
              <el-input
                v-if="editStates.studyModules[`${stageIndex}-${moduleIndex}`]"
                v-model="
                  editData.studyModules[`${stageIndex}-${moduleIndex}`]
                    .studyContent
                "
                type="textarea"
                :rows="4"
                placeholder="请输入学习内容"
                class="edit-textarea"
              />
              <!-- 显示模式 -->
              <p v-else class="study-item-content">
                <span
                  v-text="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyContent.text
                  "
                ></span>
                <span
                  v-if="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyContent.isTyping
                  "
                  class="typing-cursor"
                  >|</span
                >
              </p>
            </div>

            <!-- 学习方法 -->
            <div
              class="study-item"
              v-if="
                typewriterStates.studyPlanning.stages[stageIndex]?.modules[
                  moduleIndex
                ]?.studyMethod.text ||
                editStates.studyModules[`${stageIndex}-${moduleIndex}`]
              "
            >
              <div class="study-item-title">
                <img
                  width="26"
                  src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                  alt="菱形图标"
                />
                <span class="study-item-title-text">学习方法</span>
              </div>
              <!-- 编辑模式 -->
              <el-input
                v-if="editStates.studyModules[`${stageIndex}-${moduleIndex}`]"
                v-model="
                  editData.studyModules[`${stageIndex}-${moduleIndex}`]
                    .studyMethod
                "
                type="textarea"
                :rows="4"
                placeholder="请输入学习方法"
                class="edit-textarea"
              />
              <!-- 显示模式 -->
              <p v-else class="study-item-content">
                <span
                  v-text="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyMethod.text
                  "
                ></span>
                <span
                  v-if="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyMethod.isTyping
                  "
                  class="typing-cursor"
                  >|</span
                >
              </p>
            </div>

            <!-- 资料推荐 -->
            <div
              class="study-item"
              v-if="
                typewriterStates.studyPlanning.stages[stageIndex]?.modules[
                  moduleIndex
                ]?.studyMaterials.text ||
                editStates.studyModules[`${stageIndex}-${moduleIndex}`]
              "
            >
              <div class="study-item-title">
                <img
                  width="26"
                  src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                  alt="菱形图标"
                />
                <span class="study-item-title-text">资料推荐</span>
              </div>
              <!-- 编辑模式 -->
              <el-input
                v-if="editStates.studyModules[`${stageIndex}-${moduleIndex}`]"
                v-model="
                  editData.studyModules[`${stageIndex}-${moduleIndex}`]
                    .studyMaterials
                "
                type="textarea"
                :rows="4"
                placeholder="请输入资料推荐"
                class="edit-textarea"
              />
              <!-- 显示模式 -->
              <p v-else class="study-item-content">
                <span
                  v-text="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyMaterials.text
                  "
                ></span>
                <span
                  v-if="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyMaterials.isTyping
                  "
                  class="typing-cursor"
                  >|</span
                >
              </p>
            </div>

            <!-- 要点提醒 -->
            <div
              class="study-item"
              v-if="
                typewriterStates.studyPlanning.stages[stageIndex]?.modules[
                  moduleIndex
                ]?.studyReminder.text ||
                editStates.studyModules[`${stageIndex}-${moduleIndex}`]
              "
            >
              <div class="study-item-title">
                <img
                  width="26"
                  src="https://yqzx-1300870289.cos.ap-nanjing.myqcloud.com/storage/default/20250425/tag.png"
                  alt="菱形图标"
                />
                <span class="study-item-title-text">要点提醒</span>
              </div>
              <!-- 编辑模式 -->
              <el-input
                v-if="editStates.studyModules[`${stageIndex}-${moduleIndex}`]"
                v-model="
                  editData.studyModules[`${stageIndex}-${moduleIndex}`]
                    .studyReminder
                "
                type="textarea"
                :rows="4"
                placeholder="请输入要点提醒"
                class="edit-textarea"
              />
              <!-- 显示模式 -->
              <p v-else class="study-item-content">
                <span
                  v-text="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyReminder.text
                  "
                ></span>
                <span
                  v-if="
                    typewriterStates.studyPlanning.stages[stageIndex].modules[
                      moduleIndex
                    ].studyReminder.isTyping
                  "
                  class="typing-cursor"
                  >|</span
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 综合建议模块 -->
    <div
      class="comprehensive-advice-section"
      v-if="
        typewriterStates.comprehensiveAdvice.text ||
        editStates.comprehensiveAdvice
      "
    >
      <!-- 建议标题 -->
      <div class="step-num-tag">
        <span>04</span>
        <div class="tag-text">综合建议</div>
      </div>
      <!-- 建议内容 -->
      <div class="advice-content">
        <div class="advice-content-container">
          <!-- 编辑模式 -->
          <el-input
            v-if="editStates.comprehensiveAdvice"
            v-model="editData.comprehensiveAdvice"
            type="textarea"
            :rows="8"
            placeholder="请输入综合建议"
            class="edit-textarea"
          />
          <!-- 显示模式 -->
          <p v-else class="advice-text">
            <span v-text="typewriterStates.comprehensiveAdvice.text"></span>
            <span
              v-if="typewriterStates.comprehensiveAdvice.isTyping"
              class="typing-cursor"
              >|</span
            >
          </p>
        </div>
        <!-- 编辑按钮 -->
        <div class="edit-button-container">
          <el-button
            v-if="!editStates.comprehensiveAdvice"
            type="text"
            size="small"
            @click="startEditComprehensiveAdvice"
            :icon="Edit"
            class="edit-btn"
          >
            编辑
          </el-button>
          <div v-else class="edit-actions">
            <el-button
              size="small"
              @click="saveComprehensiveAdvice"
              :loading="loading"
              class="save-btn"
            >
              保存
            </el-button>
            <el-button size="small" @click="cancelEditComprehensiveAdvice">
              取消
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import {
  getStudyPlan,
  getStudyPlanFromDatabase,
  updateWeakModuleAnalysis,
  updateStudyModule,
  updateComprehensiveAdvice,
  saveTargetScores as saveTargetScoresApi,
} from "@/api/report";
import { ElMessage, ElMessageBox, ElNotification } from "element-plus";
import { Edit } from "@element-plus/icons-vue";

// 表单数据接口
interface FormData {
  politics: number;
  english: number;
  business1: number;
  business2: number;
}
let isVisible = ref(false);
// 薄弱模块分析接口
interface WeakModuleAnalysis {
  id?: number; // 数据库ID，用于编辑
  subject: string;
  problemAnalysis: string;
  solutions: string;
}

// 学习模块数据接口
interface StudyModule {
  id: string;
  dbId?: number; // 数据库ID，用于编辑
  name: string; // 科目名称
  studyContent: string; // 学习内容
  studyMethod: string; // 学习方法
  studyMaterials: string; // 资料推荐
  studyReminder: string; // 要点提醒
}

// 学习阶段数据接口
interface StudyStage {
  id: string;
  title: string; // 阶段标题
  modules: StudyModule[]; // 该阶段的模块数组
}

// 学习规划数据接口
interface StudyPlanning {
  title: string; // 主标题
  stages: StudyStage[]; // 阶段数组
}

// 完整的学习计划数据接口
interface StudyPlanData {
  weakModuleAnalysis: WeakModuleAnalysis[];
  studyPlanning: StudyPlanning;
  comprehensiveAdvice: string;
  comprehensiveAdviceId?: number; // 综合建议的数据库ID
}

// 分数表单数据
const formData = ref<FormData>({
  english: 0,
  politics: 0,
  business1: 0,
  business2: 0,
});

// 总分计算
const total = computed(() => {
  return (
    formData.value.politics +
    formData.value.english +
    formData.value.business1 +
    formData.value.business2
  );
});

// 学习计划数据状态
const studyPlanData = ref<StudyPlanData>({
  weakModuleAnalysis: [],
  studyPlanning: {
    title: "初试各科学习规划",
    stages: [],
  },
  comprehensiveAdvice: "",
});

// 薄弱模块分析数据（用于显示）
const weakModuleData = ref<WeakModuleAnalysis[]>([]);

// 加载状态
const loading = ref(false);

// 数据加载状态（用于整个容器的loading效果）
const dataLoading = ref(false);

// 目标分数保存状态
const savingTargetScores = ref(false);

// 当前报告ID
const currentReportId = ref<number | string>("");

// 编辑状态管理
const editStates = ref({
  weakModules: [] as boolean[], // 薄弱模块编辑状态
  studyModules: {} as Record<string, boolean>, // 学习模块编辑状态，key为stageIndex-moduleIndex
  comprehensiveAdvice: false, // 综合建议编辑状态
});

// 编辑数据缓存
const editData = ref({
  weakModules: [] as WeakModuleAnalysis[], // 薄弱模块编辑数据
  studyModules: {} as Record<string, StudyModule>, // 学习模块编辑数据
  comprehensiveAdvice: "", // 综合建议编辑数据
});

// 打字机效果相关状态
const typewriterStates = ref({
  weakModules: [] as Array<{
    subject: { text: string; isTyping: boolean };
    problemAnalysis: { text: string; isTyping: boolean };
    solutions: { text: string; isTyping: boolean };
  }>,
  studyPlanning: {
    stages: [] as Array<{
      title: { text: string; isTyping: boolean };
      modules: Array<{
        name: { text: string; isTyping: boolean };
        studyContent: { text: string; isTyping: boolean };
        studyMethod: { text: string; isTyping: boolean };
        studyMaterials: { text: string; isTyping: boolean };
        studyReminder: { text: string; isTyping: boolean };
      }>;
    }>,
  },
  comprehensiveAdvice: { text: "", isTyping: false },
});

// 学习规划数据（用于显示）
const studyPlanningData = ref<StudyPlanning>({
  title: "初试各科学习规划",
  stages: [],
});

// 打字机效果函数
const typewriterEffect = (
  text: string,
  targetRef: { text: string; isTyping: boolean },
  speed = 80
) => {
  return new Promise<void>((resolve) => {
    targetRef.isTyping = true;
    targetRef.text = "";
    let index = 0;

    const timer = setInterval(() => {
      if (index < text.length) {
        targetRef.text += text[index];
        index++;
      } else {
        clearInterval(timer);
        targetRef.isTyping = false;
        resolve();
      }
    }, speed);
  });
};

// 渲染薄弱模块分析
const renderWeakModuleAnalysis = async (data: WeakModuleAnalysis[]) => {
  weakModuleData.value = data;

  // 初始化打字机状态
  typewriterStates.value.weakModules = data.map(() => ({
    subject: { text: "", isTyping: false },
    problemAnalysis: { text: "", isTyping: false },
    solutions: { text: "", isTyping: false },
  }));

  // 依次渲染每个薄弱模块
  for (let moduleIndex = 0; moduleIndex < data.length; moduleIndex++) {
    const module = data[moduleIndex];
    const moduleState = typewriterStates.value.weakModules[moduleIndex];

    console.log(`渲染薄弱模块 ${moduleIndex + 1}: ${module.subject}`);

    // 依次渲染各个部分，使用更慢的速度让效果更明显
    await typewriterEffect(module.subject, moduleState.subject, 80);
    await typewriterEffect(
      module.problemAnalysis,
      moduleState.problemAnalysis,
      40
    );
    await typewriterEffect(module.solutions, moduleState.solutions, 40);
  }
};

// 渲染学习规划
const renderStudyPlanning = async (data: StudyPlanning) => {
  console.log("renderStudyPlanning 开始，数据:", data);
  studyPlanningData.value = data;

  // 初始化打字机状态
  typewriterStates.value.studyPlanning.stages = data.stages.map((stage) => ({
    title: { text: "", isTyping: false },
    modules: stage.modules.map(() => ({
      name: { text: "", isTyping: false },
      studyContent: { text: "", isTyping: false },
      studyMethod: { text: "", isTyping: false },
      studyMaterials: { text: "", isTyping: false },
      studyReminder: { text: "", isTyping: false },
    })),
  }));

  console.log(
    "打字机状态初始化完成，阶段数量:",
    typewriterStates.value.studyPlanning.stages.length
  );

  // 依次渲染每个阶段
  for (let stageIndex = 0; stageIndex < data.stages.length; stageIndex++) {
    const stage = data.stages[stageIndex];
    const stageState = typewriterStates.value.studyPlanning.stages[stageIndex];

    console.log(`开始渲染阶段 ${stageIndex + 1}: ${stage.title}`);
    // 渲染阶段标题
    await typewriterEffect(stage.title, stageState.title, 80);

    // 渲染每个模块
    for (
      let moduleIndex = 0;
      moduleIndex < stage.modules.length;
      moduleIndex++
    ) {
      const module = stage.modules[moduleIndex];
      const moduleState = stageState.modules[moduleIndex];

      console.log(`  渲染模块 ${moduleIndex + 1}: ${module.name}`);
      await typewriterEffect(module.name, moduleState.name, 80);
      await typewriterEffect(module.studyContent, moduleState.studyContent, 25);
      await typewriterEffect(module.studyMethod, moduleState.studyMethod, 25);
      await typewriterEffect(
        module.studyMaterials,
        moduleState.studyMaterials,
        25
      );
      await typewriterEffect(
        module.studyReminder,
        moduleState.studyReminder,
        25
      );
    }
  }
  console.log("renderStudyPlanning 完成");
};

// 渲染综合建议
const renderComprehensiveAdvice = async (text: string) => {
  await typewriterEffect(text, typewriterStates.value.comprehensiveAdvice, 20);
  ElNotification({
    title: "通知",
    message: "报告生成完毕",
    type: "success",
  });
};

// 从数据库获取数据并渲染
const fetchAndRenderStudyPlan = async (reportId: number | string) => {
  try {
    // 调用API从数据库获取学习计划数据
    const response = await getStudyPlanFromDatabase({ report_id: reportId });
    if (response.code === 0 && response.data) {
      const data = response.data as StudyPlanData;

      // 更新主数据
      studyPlanData.value = data;

      console.log("开始渲染学习计划数据:", data);

      // 数据获取成功，立即移除loading，开始打字机效果
      dataLoading.value = false;

      // 依次渲染各个部分
      if (data.weakModuleAnalysis && data.weakModuleAnalysis.length > 0) {
        console.log(
          "渲染薄弱模块分析，模块数量:",
          data.weakModuleAnalysis.length
        );
        await renderWeakModuleAnalysis(data.weakModuleAnalysis);
      }

      console.log("渲染学习规划，阶段数量:", data.studyPlanning.stages.length);
      await renderStudyPlanning(data.studyPlanning);

      console.log("渲染综合建议");
      await renderComprehensiveAdvice(data.comprehensiveAdvice);
    } else {
      console.error("获取学习计划失败:", response.msg);
      ElMessage.error(response.msg || "获取学习计划失败");
      // 如果获取数据失败，也要移除loading
      dataLoading.value = false;
    }
  } catch (error) {
    console.error("获取学习计划异常:", error);
    ElMessage.error("获取学习计划失败");
    // 如果发生异常，也要移除loading
    dataLoading.value = false;
  }
};

// 重置状态
const resetStates = () => {
  // 重置所有状态
  weakModuleData.value = [];
  studyPlanningData.value = {
    title: "初试各科学习规划",
    stages: [],
  };

  // 重置打字机状态
  typewriterStates.value = {
    weakModules: [],
    studyPlanning: {
      stages: [],
    },
    comprehensiveAdvice: { text: "", isTyping: false },
  };
};

// 主要的获取学习计划方法（暴露给父组件）
const fetchStudyPlan = async (reportId: number | string) => {
  try {
    // 开始整体加载，先显示组件和loading
    dataLoading.value = true;
    isVisible.value = true;

    loading.value = true;
    // 保存当前报告ID
    currentReportId.value = reportId;
    // 调用API生成学习计划并保存到数据库
    const response = await getStudyPlan({ report_id: reportId });
    if (response.code === 0) {
      // 数据已经保存到数据库，直接获取数据并渲染
      // 注意：fetchAndRenderStudyPlan 内部会控制 dataLoading 状态
      await fetchAndRenderStudyPlan(reportId);
    } else {
      console.error("生成学习计划失败:", response.msg);
      ElMessage.error(response.msg || "生成学习计划失败");
      // 如果失败，隐藏组件
      isVisible.value = false;
      dataLoading.value = false;
    }
  } catch (error) {
    console.error("获取学习计划异常:", error);
    ElMessage.error("获取学习计划失败");
    // 如果异常，隐藏组件
    isVisible.value = false;
    dataLoading.value = false;
  } finally {
    loading.value = false;
  }
};

// 编辑相关方法
// 开始编辑薄弱模块
const startEditWeakModule = (moduleIndex: number) => {
  // 初始化编辑状态
  editStates.value.weakModules[moduleIndex] = true;

  // 初始化编辑数据，直接使用原始数据（包含\n换行符）
  const originalData = weakModuleData.value[moduleIndex];
  editData.value.weakModules[moduleIndex] = {
    id: originalData.id,
    subject: originalData.subject,
    problemAnalysis: originalData.problemAnalysis,
    solutions: originalData.solutions,
  };
};

// 保存薄弱模块编辑
const saveWeakModule = async (moduleIndex: number) => {
  try {
    loading.value = true;

    const editItem = editData.value.weakModules[moduleIndex];
    if (!editItem.id) {
      ElMessage.error("缺少数据ID，无法保存");
      return;
    }

    const response = await updateWeakModuleAnalysis({
      id: editItem.id,
      subject: editItem.subject,
      problem_analysis: editItem.problemAnalysis,
      solutions: editItem.solutions,
    });

    if (response.code === 0) {
      // 更新显示数据（直接使用编辑数据）
      weakModuleData.value[moduleIndex] = { ...editItem };

      // 更新打字机状态（直接使用编辑数据）
      typewriterStates.value.weakModules[moduleIndex] = {
        subject: { text: editItem.subject, isTyping: false },
        problemAnalysis: { text: editItem.problemAnalysis, isTyping: false },
        solutions: { text: editItem.solutions, isTyping: false },
      };

      // 退出编辑模式
      editStates.value.weakModules[moduleIndex] = false;

      ElMessage.success("保存成功");
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存薄弱模块失败:", error);
    ElMessage.error("保存失败");
  } finally {
    loading.value = false;
  }
};

// 取消编辑薄弱模块
const cancelEditWeakModule = (moduleIndex: number) => {
  editStates.value.weakModules[moduleIndex] = false;
  delete editData.value.weakModules[moduleIndex];
};

// 开始编辑学习模块
const startEditStudyModule = (stageIndex: number, moduleIndex: number) => {
  const key = `${stageIndex}-${moduleIndex}`;
  editStates.value.studyModules[key] = true;

  const originalData =
    studyPlanningData.value.stages[stageIndex].modules[moduleIndex];
  editData.value.studyModules[key] = {
    id: originalData.id,
    dbId: originalData.dbId,
    name: originalData.name,
    studyContent: originalData.studyContent,
    studyMethod: originalData.studyMethod,
    studyMaterials: originalData.studyMaterials,
    studyReminder: originalData.studyReminder,
  };
};

// 保存学习模块编辑
const saveStudyModule = async (stageIndex: number, moduleIndex: number) => {
  try {
    loading.value = true;

    const key = `${stageIndex}-${moduleIndex}`;
    const editItem = editData.value.studyModules[key];

    if (!editItem.dbId) {
      ElMessage.error("缺少数据ID，无法保存");
      return;
    }

    const response = await updateStudyModule({
      id: editItem.dbId,
      name: editItem.name,
      study_content: editItem.studyContent,
      study_method: editItem.studyMethod,
      study_materials: editItem.studyMaterials,
      study_reminder: editItem.studyReminder,
    });

    if (response.code === 0) {
      // 更新显示数据（直接使用编辑数据）
      studyPlanningData.value.stages[stageIndex].modules[moduleIndex] = {
        ...editItem,
      };

      // 更新打字机状态（直接使用编辑数据）
      const moduleState =
        typewriterStates.value.studyPlanning.stages[stageIndex].modules[
          moduleIndex
        ];
      moduleState.name = { text: editItem.name, isTyping: false };
      moduleState.studyContent = {
        text: editItem.studyContent,
        isTyping: false,
      };
      moduleState.studyMethod = { text: editItem.studyMethod, isTyping: false };
      moduleState.studyMaterials = {
        text: editItem.studyMaterials,
        isTyping: false,
      };
      moduleState.studyReminder = {
        text: editItem.studyReminder,
        isTyping: false,
      };

      // 退出编辑模式
      editStates.value.studyModules[key] = false;

      ElMessage.success("保存成功");
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存学习模块失败:", error);
    ElMessage.error("保存失败");
  } finally {
    loading.value = false;
  }
};

// 取消编辑学习模块
const cancelEditStudyModule = (stageIndex: number, moduleIndex: number) => {
  const key = `${stageIndex}-${moduleIndex}`;
  editStates.value.studyModules[key] = false;
  delete editData.value.studyModules[key];
};

// 开始编辑综合建议
const startEditComprehensiveAdvice = () => {
  editStates.value.comprehensiveAdvice = true;
  editData.value.comprehensiveAdvice = studyPlanData.value.comprehensiveAdvice;
};

// 保存综合建议编辑
const saveComprehensiveAdvice = async () => {
  try {
    loading.value = true;

    if (!studyPlanData.value.comprehensiveAdviceId) {
      ElMessage.error("缺少数据ID，无法保存");
      return;
    }

    const response = await updateComprehensiveAdvice({
      id: studyPlanData.value.comprehensiveAdviceId,
      advice_content: editData.value.comprehensiveAdvice,
    });

    if (response.code === 0) {
      // 更新显示数据（直接使用编辑数据）
      studyPlanData.value.comprehensiveAdvice =
        editData.value.comprehensiveAdvice;

      // 更新打字机状态（直接使用编辑数据）
      typewriterStates.value.comprehensiveAdvice = {
        text: editData.value.comprehensiveAdvice,
        isTyping: false,
      };

      // 退出编辑模式
      editStates.value.comprehensiveAdvice = false;

      ElMessage.success("保存成功");
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存综合建议失败:", error);
    ElMessage.error("保存失败");
  } finally {
    loading.value = false;
  }
};

// 取消编辑综合建议
const cancelEditComprehensiveAdvice = () => {
  editStates.value.comprehensiveAdvice = false;
  editData.value.comprehensiveAdvice = "";
};

// 保存目标分数
const saveTargetScores = async () => {
  try {
    if (!currentReportId.value) {
      ElMessage.error("缺少报告ID，无法保存");
      return;
    }

    savingTargetScores.value = true;

    const response = await saveTargetScoresApi({
      report_id: currentReportId.value,
      politics: formData.value.politics || 0,
      english: formData.value.english || 0,
      business1: formData.value.business1 || 0,
      business2: formData.value.business2 || 0,
      total: total.value || 0,
    });

    if (response.code === 0) {
      ElMessage.success("目标分数保存成功");
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存目标分数失败:", error);
    ElMessage.error("保存失败");
  } finally {
    savingTargetScores.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  fetchStudyPlan,
  fetchAndRenderStudyPlan,
  resetStates,
});
</script>

<style lang="less" scoped>
.weak-module-analysis {
  width: 100%;
  padding: 0 160px;
  padding-bottom: 120px;
}

/* 复用现有的 step-num-tag 样式 */
.step-num-tag {
  background-image: url("@/assets/images/subtitlebg.png");
  width: 265px;
  height: 40px;
  margin-top: 20px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.weak-module-container {
  border: 1px solid #1bb394;
  padding: 20px;
  border-radius: 12px;

  .module-header {
    margin-top: 10px;
  }
}

.step-num-tag span {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  border-radius: 50%;
  margin-right: 30px;
  font-weight: bold;
  padding-left: 13px;
}

.tag-text {
  color: #1bb394;
  font-weight: bold;
}

/* 清理不需要的样式 */

/* 分析区域样式 */
.detail-section,
.analysis-section,
.solutions-section {
  margin-bottom: 20px;

  .subtitle {
    margin-top: 15px;
    width: 100px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 4px;

    .subtitle-text {
      height: 20px;
      line-height: 20px;
      font-weight: bold;
      font-size: 16px;
      color: #5a5a5a;
    }
  }

  &:last-child {
    margin-bottom: 0;
  }

  .detail-text,
  .analysis-text {
    padding-left: 20px;
    font-size: 14px;
    color: #5a5a5a;
    text-align: justify;
    white-space: pre-wrap; /* 保持换行符和空格 */
  }

  .solutions-content {
    padding-left: 20px;

    .solution-text {
      font-size: 14px;
      color: #5a5a5a;
      text-align: justify;
      margin: 0;
      white-space: pre-wrap; /* 保持换行符和空格 */
    }
  }
}
.subtitle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 目标分数表格样式 */
.target-score-section {
  margin-top: 30px;

  .target-score-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    .target-score-title-container {
      display: flex;
      align-items: center;
      gap: 4px;
    }
    .target-score-title {
      height: 20px;
      line-height: 20px;
      font-weight: bold;
      font-size: 16px;
      color: #5a5a5a;
    }

    .target-score-actions {
      display: flex;
      align-items: center;
    }
  }

  .target-score-table {
    border: 1px solid #1bb394;
    border-radius: 8px;
    overflow: hidden;

    .score-row {
      display: flex;

      &.header-row {
        background-color: #f5fffd;

        .score-cell {
          font-size: 12px;
          color: #504e4e;
          font-weight: bold;
          text-align: center;
        }
      }

      &.data-row {
        .score-cell {
          font-size: 12px;
          color: #504e4e;
          text-align: center;
          font-weight: 500;
        }
      }

      .score-cell {
        flex: 1;
        padding: 12px 8px;
        border-right: 1px solid #1bb394;

        font-size: 14px;

        &:last-child {
          border-right: none;
        }
      }

      .bottom-cell {
        border-top: 1px solid #1bb394;

        .score-input {
          border: none;
          outline: none;
          background: transparent;
          width: 100%;
          text-align: center;
          font-size: 12px;
          color: #504e4e;
          font-weight: 500;
        }
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .weak-module-analysis {
    padding: 15px;
  }

  .step-num-tag {
    width: 220px;
    height: 35px;
  }

  .weak-module-container {
    padding: 15px;
  }

  .target-score-section {
    margin-top: 20px;

    .target-score-table {
      .score-row {
        .score-cell {
          padding: 10px 6px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 学习规划模块样式 */
.study-planning-section {
  margin-top: 40px;

  .planning-main-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;

    .planning-title-text {
      font-size: 18px;
      font-weight: bold;
      color: #1bb394;
    }
  }

  .stage-section {
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .stage-title {
    font-weight: bold;
    font-size: 20px;
    color: #4c5370;
    margin-bottom: 20px;
  }

  .stage-content {
    border: 1px solid #1bb394;
    border-radius: 12px;
    padding-top: 20px;
  }

  .module-container {
    padding: 0 20px;
    padding-bottom: 20px;

    .module-title {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .module-title-text {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .study-item {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .study-item-title {
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 8px;

        .study-item-title-text {
          font-size: 16px;
          font-weight: bold;
          color: #5a5a5a;
        }
      }

      .study-item-content {
        padding-left: 26px;
        font-size: 14px;
        color: #5a5a5a;
        line-height: 30px;
        text-align: justify;
        margin: 0;
        white-space: pre-wrap; /* 保持换行符和空格 */
      }
    }
  }
}

/* 学习规划模块响应式设计 */
@media (max-width: 768px) {
  .study-planning-section {
    margin-top: 30px;

    .planning-main-title {
      .planning-title-text {
        font-size: 16px;
      }
    }

    .stage-title {
      font-size: 14px;
    }

    .module-container {
      padding: 15px;

      .module-title {
        .module-title-text {
          font-size: 14px;
        }
      }

      .study-item {
        margin-bottom: 15px;

        .study-item-title {
          .study-item-title-text {
            font-size: 13px;
          }
        }

        .study-item-content {
          padding-left: 20px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 综合建议模块样式 */
.comprehensive-advice-section {
  margin-top: 40px;

  .advice-content {
    border: 1px solid #1bb394;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .advice-content-container {
      width: 86%;
    }
    .advice-text {
      font-size: 14px;
      color: #5a5a5a;
      text-align: justify;
      line-height: 30px;
      margin: 0;
      white-space: pre-wrap; /* 保持换行符和空格 */
    }
  }
}

/* 综合建议模块响应式设计 */
@media (max-width: 768px) {
  .comprehensive-advice-section {
    margin-top: 30px;

    .advice-content {
      padding: 15px;

      .advice-text {
        font-size: 12px;
      }
    }
  }
}

/* 打字机光标样式 */
.typing-cursor {
  display: inline-block;
  animation: blink 1s infinite;
  color: #1bb394;
  font-weight: bold;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 编辑功能样式 */
.edit-button-container {
  height: 30px;
  display: flex;
  justify-content: flex-end;
  .edit-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    gap: 8px;
  }
}

/* 编辑按钮样式 */
.edit-btn {
  color: #1bb394 !important;
  border: none !important;
  background: none !important;

  &:hover {
    color: #16a085 !important;
    background: rgba(27, 179, 148, 0.1) !important;
  }
}

/* 保存按钮样式 */
.save-btn {
  background-color: #1bb394 !important;
  border-color: #1bb394 !important;
  color: white !important;

  &:hover {
    background-color: #16a085 !important;
    border-color: #16a085 !important;
  }
}

/* 编辑模式下的输入框样式 */
.edit-input {
  :deep(.el-input__wrapper) {
    border: 1px solid #ff4757 !important;
    border-radius: 4px;
    box-shadow: 0 0 0 1px rgba(255, 71, 87, 0.2) !important;
    &.is-focus {
      border-color: #ff4757 !important;
      box-shadow: 0 0 0 1px rgba(255, 71, 87, 0.2) !important;
    }
  }
}

.edit-textarea {
  margin-bottom: 10px;

  :deep(.el-textarea__inner) {
    font-family: inherit;
    line-height: 1.6;
    border: 1px solid #ff4757 !important;
    border-radius: 4px;
    box-shadow: 0 0 0 1px rgba(255, 71, 87, 0.2) !important;
    &:focus {
      border-color: #ff4757 !important;
    }
  }
}
</style>
